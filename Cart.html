<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - Order Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .focus-ring {
            @apply focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2;
        }
        
        .btn-primary {
            @apply bg-yellow-500 hover:bg-yellow-600 focus:bg-yellow-600;
        }
        
        .form-input {
            @apply focus:border-yellow-500 focus:ring-yellow-500;
        }
        
        .card-hover {
            @apply hover:shadow-lg transition-shadow duration-200;
        }
        
        .nav-link {
            @apply hover:bg-yellow-500 hover:bg-opacity-20;
        }
        
        .toast {
            @apply transform translate-x-full transition-transform duration-300;
        }
        
        .toast.show {
            @apply translate-x-0;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #f2b90c;
            border-radius: 3px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #d4a00a;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Mobile Overlay -->
    <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden" onclick="closeMobileMenu()"></div>

    <div class="flex h-screen bg-gray-100">
        <!-- Sidebar -->
        <aside id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 text-white transform -translate-x-full transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 lg:w-20 lg:hover:w-64 group">
            <!-- Sidebar Header -->
            <div class="flex items-center justify-between h-16 lg:h-20 px-4 border-b border-gray-700">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center font-bold text-lg">
                        L
                    </div>
                    <div class="nav-text opacity-0 lg:group-hover:opacity-100 transition-opacity duration-200">
                        <h1 class="font-bold text-lg whitespace-nowrap">LohiaCorp</h1>
                        <p class="text-xs text-gray-400 whitespace-nowrap">Order Management</p>
                    </div>
                </div>
                <button id="mobile-close-btn" class="lg:hidden p-2 rounded-lg hover:bg-gray-700 focus-ring">
                    <i data-lucide="x" class="w-5 h-5"></i>
                </button>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                <!-- Main Navigation -->
                <div class="space-y-2">
                    <ul class="space-y-2">
                        <li>
                            <a href="HomePage.html" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Dashboard">
                                <i data-lucide="layout-dashboard" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 lg:group-hover:opacity-100 font-medium">Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="HomePage.html" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Part Finder">
                                <i data-lucide="search" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 lg:group-hover:opacity-100 font-medium">Part Finder</span>
                            </a>
                        </li>
                        <li>
                            <a href="Cart.html" class="nav-link flex items-center gap-3 px-3 py-3 bg-yellow-500 bg-opacity-20 text-yellow-300 rounded-xl transition-all duration-200 group focus-ring relative" role="menuitem" aria-label="Shopping Cart">
                                <div class="relative">
                                    <i data-lucide="shopping-cart" class="w-5 h-5 flex-shrink-0"></i>
                                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center text-xs font-bold" id="cart-count">0</span>
                                </div>
                                <span class="nav-text whitespace-nowrap opacity-0 lg:group-hover:opacity-100 font-medium">Cart</span>
                                <span class="nav-text ml-auto opacity-0 lg:group-hover:opacity-100">
                                    <span class="bg-green-600 text-green-100 text-xs px-2 py-1 rounded-full">Active</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-2 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Track Orders">
                                <i data-lucide="truck" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 lg:group-hover:opacity-100 font-medium">Track Orders</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Recently Used Section -->
                <div class="pt-4 border-t border-gray-600">
                    <h4 class="nav-text text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 opacity-0 lg:group-hover:opacity-100">Recently Used</h4>
                    <ul class="space-y-2">
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Part Ordering">
                                <i data-lucide="package-plus" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 lg:group-hover:opacity-100 font-medium">Part Ordering</span>
                                <span class="nav-text ml-auto opacity-0 lg:group-hover:opacity-100">
                                    <span class="text-xs text-gray-400">2h ago</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Created Orders">
                                <i data-lucide="file-check-2" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 lg:group-hover:opacity-100 font-medium">Created Orders</span>
                                <span class="nav-text ml-auto opacity-0 lg:group-hover:opacity-100">
                                    <span class="text-xs text-gray-400">1d ago</span>
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Other Features Section -->
                <div class="pt-4 border-t border-gray-600">
                    <h4 class="nav-text text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 opacity-0 lg:group-hover:opacity-100">Other Features</h4>
                    <ul class="space-y-2">
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Closed Orders">
                                <i data-lucide="archive" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 lg:group-hover:opacity-100 font-medium">Closed Orders</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="About Us">
                                <i data-lucide="info" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 lg:group-hover:opacity-100 font-medium">About Us</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer / User Info -->
            <div class="p-4 border-t border-gray-700 shrink-0">
                <a href="#" class="flex items-center gap-3 group hover:bg-gray-700 rounded-xl p-2 transition-all duration-200 focus-ring" aria-label="User profile and logout">
                    <img src="https://placehold.co/40x40/E2E8F0/4A5568?text=K" alt="User Avatar" class="w-10 h-10 rounded-full flex-shrink-0 ring-2 ring-gray-600 group-hover:ring-yellow-500 transition-all duration-200" loading="lazy">
                    <div class="flex-1 nav-text opacity-0 lg:group-hover:opacity-100 transition-opacity duration-200">
                        <p class="font-semibold text-white text-sm whitespace-nowrap">Kanpur Plastipack</p>
                        <p class="text-xs text-gray-400 whitespace-nowrap">ID: 10340</p>
                    </div>
                    <i data-lucide="log-out" class="nav-text opacity-0 lg:group-hover:opacity-100 transition-all duration-200 group-hover:text-red-400"></i>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main id="main-content" class="flex-1 flex flex-col h-screen lg:ml-20 transition-all duration-300">
            <!-- Header -->
            <header class="h-16 lg:h-20 bg-white border-b border-gray-200 flex flex-col justify-center px-4 sm:px-6 lg:px-8 shrink-0 shadow-sm">
                <!-- Top Header Row -->
                <div class="flex items-center justify-between">
                    <!-- Mobile Menu Button & Title -->
                    <div class="flex items-center gap-4">
                        <button id="mobile-menu-btn" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 focus-ring transition-colors" aria-label="Open navigation menu">
                            <i data-lucide="menu" class="w-6 h-6 text-gray-600"></i>
                            <span class="sr-only">Menu</span>
                        </button>
                        <div>
                            <h1 class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 tracking-tight">Order Management System</h1>
                            <p class="text-xs sm:text-sm text-gray-500 hidden sm:block"></p>
                        </div>
                    </div>

                    <!-- Header Actions -->
                    <div class="flex items-center gap-2 sm:gap-4">
                        <!-- Notifications -->
                        <button class="relative p-2 rounded-lg hover:bg-gray-100 focus-ring transition-colors hidden sm:flex items-center gap-2" aria-label="View notifications">
                            <i data-lucide="bell" class="w-5 h-5 text-gray-600"></i>
                            <span class="hidden lg:inline text-sm text-gray-600">Notifications</span>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">2</span>
                        </button>

                        <!-- Activity Log -->
                        <button class="flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 px-3 py-2 rounded-lg transition-all duration-200 focus-ring" onclick="showActivityLog()" aria-label="View activity log">
                            <i data-lucide="activity" class="w-5 h-5" aria-hidden="true"></i>
                            <span class="hidden lg:inline">Activity Log</span>
                        </button>

                        <!-- Parts Policy -->
                        <button class="flex items-center gap-2 text-sm font-medium text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50 px-3 py-2 rounded-lg transition-all duration-200 focus-ring">
                            <i data-lucide="shield-check" class="w-5 h-5"></i>
                            <span class="hidden sm:inline">Parts Policy</span>
                        </button>

                        <div class="w-px h-6 bg-gray-300 hidden md:block"></div>

                        <!-- User Info -->
                        <div class="hidden md:flex items-center gap-3">
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-700">Welcome, Kanpur Plastipack Limited</div>
                                <div class="text-xs text-gray-500"><EMAIL></div>
                            </div>
                            <button class="w-10 h-10 rounded-full bg-yellow-500 text-white flex items-center justify-center focus-ring font-semibold" aria-label="User profile menu">
                                K
                            </button>
                        </div>

                        <!-- Mobile User Avatar -->
                        <button class="md:hidden w-8 h-8 rounded-full bg-yellow-500 text-white flex items-center justify-center focus-ring font-semibold" aria-label="User menu">
                            K
                        </button>
                    </div>
                </div>

                <!-- Breadcrumbs -->
                <nav class="flex items-center space-x-2 text-sm text-gray-500 mt-2" aria-label="Breadcrumb">
                    <a href="HomePage.html" class="flex items-center gap-1 hover:text-gray-700 transition-colors">
                        <i data-lucide="home" class="w-4 h-4"></i>
                        <span>Home</span>
                    </a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <span id="current-page" class="text-gray-900 font-medium">Shopping Cart</span>
                </nav>
            </header>

            <!-- Page Content -->
            <div class="flex-1 p-2 sm:p-3 lg:p-4 overflow-y-auto bg-gray-50">
                <div class="w-full space-y-3">
                    <!-- Cart Header -->
                    <div class="bg-yellow-500 rounded-xl p-4 text-white shadow-xl animate-fade-in">
                        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
                            <div>
                                <h2 class="text-lg sm:text-xl font-bold mb-1">Shopping Cart Management</h2>
                                <p class="text-yellow-100 text-sm">Manage your saved carts and proceed to checkout</p>
                            </div>
                            <div class="flex items-center gap-4">
                                <button onclick="clearAllCarts()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors font-medium flex items-center gap-2">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                    Clear All
                                </button>
                                <div class="flex items-center gap-2 text-yellow-100">
                                    <i data-lucide="clock" class="w-4 h-4"></i>
                                    <span class="text-sm" id="current-time"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cart Statistics -->
                    <div class="bg-white rounded-xl shadow-lg p-6 animate-fade-in">
                        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <p class="text-2xl font-bold text-gray-900" id="total-carts">0</p>
                                <p class="text-sm text-gray-600">Total Carts</p>
                            </div>
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <p class="text-2xl font-bold text-yellow-600" id="total-items">0</p>
                                <p class="text-sm text-gray-600">Total Items</p>
                            </div>
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <p class="text-2xl font-bold text-green-600" id="total-value">₹0.00</p>
                                <p class="text-sm text-gray-600">Total Value</p>
                            </div>
                            <div class="text-center p-4 bg-gray-50 rounded-lg">
                                <p class="text-2xl font-bold text-red-600" id="pending-carts">0</p>
                                <p class="text-sm text-gray-600">Pending Orders</p>
                            </div>
                        </div>
                    </div>

                    <!-- Main Content Grid -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                        <!-- Cart List -->
                        <div class="lg:col-span-2">
                            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                                <div class="bg-gray-50 p-4 border-b">
                                    <h3 class="text-lg font-semibold text-gray-900">Saved Carts</h3>
                                    <p class="text-sm text-gray-600">Manage your saved shopping carts</p>
                                </div>
                                <div class="p-6">
                                    <div id="cart-list-container">
                                        <!-- Cart items will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="lg:col-span-1">
                            <div class="bg-white rounded-xl shadow-lg p-6">
                                <div class="flex items-center justify-between mb-6">
                                    <h3 class="text-lg font-semibold text-gray-800">Recent Activity</h3>
                                    <button class="text-sm text-yellow-600 hover:text-yellow-800 font-medium focus-ring rounded-lg px-3 py-1">View All</button>
                                </div>
                                <div class="space-y-4" id="recent-activity-container">
                                    <!-- Dynamic recent activity will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Activity Log Modal -->
    <div id="activity-log-modal" class="fixed inset-0 z-50 hidden" role="dialog" aria-modal="true" aria-labelledby="activity-log-title">
        <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300" onclick="closeActivityLog()"></div>
        <div class="relative flex items-center justify-center min-h-screen p-4">
            <div class="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-2xl shadow-2xl transform transition-all duration-300 overflow-hidden">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-yellow-50">
                    <div class="flex items-center gap-3">
                        <div class="p-3 bg-yellow-100 rounded-lg">
                            <i data-lucide="activity" class="w-6 h-6 text-yellow-600" aria-hidden="true"></i>
                        </div>
                        <div>
                            <h3 id="activity-log-title" class="text-xl font-semibold text-gray-900">Activity Log</h3>
                            <p class="text-sm text-gray-600">Complete history of user actions</p>
                        </div>
                    </div>
                    <button onclick="closeActivityLog()" class="p-2 text-gray-500 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors focus-ring" aria-label="Close activity log">
                        <i data-lucide="x" class="w-5 h-5" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Modal Content -->
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <div id="activity-log-content" class="space-y-4">
                        <!-- Activity items will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // --- Global Variables ---
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const navTexts = document.querySelectorAll('.nav-text');
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileCloseBtn = document.getElementById('mobile-close-btn');
        const mobileOverlay = document.getElementById('mobile-overlay');
        const toastContainer = document.getElementById('toast-container');

        // --- Activity Logging System ---
        let activityLog = JSON.parse(localStorage.getItem('userActivityLog') || '[]');

        function logActivity(action, details = {}) {
            const activity = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                action: action,
                details: details,
                user: 'Kanpur Plastipack Limited'
            };

            activityLog.unshift(activity);

            // Keep only last 100 activities
            if (activityLog.length > 100) {
                activityLog = activityLog.slice(0, 100);
            }

            localStorage.setItem('userActivityLog', JSON.stringify(activityLog));
            updateRecentActivity();
        }

        // --- Utility Functions ---
        function showToast(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            const bgColor = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-yellow-600'
            }[type] || 'bg-yellow-600';

            toast.className = `toast ${bgColor} text-white px-6 py-4 rounded-lg shadow-lg flex items-center gap-3 mb-2`;
            toast.innerHTML = `
                <i data-lucide="${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : type === 'warning' ? 'alert-triangle' : 'info'}" class="w-5 h-5"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.remove()" class="ml-auto hover:bg-white hover:bg-opacity-20 rounded p-1">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            `;

            toastContainer.appendChild(toast);
            lucide.createIcons();

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Auto remove
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, duration);
        }

        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // --- Mobile Navigation Logic ---
        function openMobileMenu() {
            sidebar.classList.remove('-translate-x-full');
            mobileOverlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeMobileMenu() {
            sidebar.classList.add('-translate-x-full');
            mobileOverlay.classList.add('hidden');
            document.body.style.overflow = '';
        }

        // --- Desktop Sidebar Logic (Hover-based) ---
        const expandSidebar = () => {
            if (window.innerWidth >= 1024) { // Only on desktop
                sidebar.classList.remove('lg:w-20');
                sidebar.classList.add('lg:w-64');
                mainContent.classList.remove('lg:ml-20');
                mainContent.classList.add('lg:ml-64');
                navTexts.forEach(text => {
                    text.classList.remove('opacity-0');
                });
            }
        };

        const collapseSidebar = () => {
            if (window.innerWidth >= 1024) { // Only on desktop
                sidebar.classList.add('lg:w-20');
                sidebar.classList.remove('lg:w-64');
                mainContent.classList.add('lg:ml-20');
                mainContent.classList.remove('lg:ml-64');
                navTexts.forEach(text => {
                    text.classList.add('opacity-0');
                });
            }
        };

        // --- Event Listeners ---
        mobileMenuBtn?.addEventListener('click', openMobileMenu);
        mobileCloseBtn?.addEventListener('click', closeMobileMenu);
        mobileOverlay?.addEventListener('click', closeMobileMenu);

        // Desktop sidebar hover
        sidebar.addEventListener('mouseenter', expandSidebar);
        sidebar.addEventListener('mouseleave', collapseSidebar);

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 1024) {
                closeMobileMenu();
            }
        });

        // Update time every minute
        updateCurrentTime();
        setInterval(updateCurrentTime, 60000);

        // --- Recent Activity Functions ---
        function updateRecentActivity() {
            const container = document.getElementById('recent-activity-container');
            if (!container) return;

            const recentActivities = activityLog.slice(0, 5);

            if (recentActivities.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <p class="text-gray-500 text-sm">No recent activity</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = recentActivities.map(activity => {
                const timeAgo = getTimeAgo(new Date(activity.timestamp));
                const actionText = activity.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

                return `
                    <div class="flex items-start gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <div class="p-2 bg-yellow-100 rounded-lg flex-shrink-0">
                            <i data-lucide="activity" class="w-4 h-4 text-yellow-600"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">${actionText}</p>
                            <p class="text-xs text-gray-500">${timeAgo}</p>
                            ${activity.details && Object.keys(activity.details).length > 0 ?
                                `<p class="text-xs text-gray-400 mt-1 truncate">${JSON.stringify(activity.details).substring(0, 50)}...</p>` : ''}
                        </div>
                    </div>
                `;
            }).join('');

            lucide.createIcons();
        }

        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
            return `${Math.floor(diffInSeconds / 86400)}d ago`;
        }

        // --- Activity Log Modal ---
        function showActivityLog() {
            const modal = document.getElementById('activity-log-modal');
            const content = document.getElementById('activity-log-content');

            // Populate activity log
            content.innerHTML = activityLog.map(activity => `
                <div class="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div class="p-2 bg-yellow-100 rounded-lg flex-shrink-0">
                        <i data-lucide="activity" class="w-4 h-4 text-yellow-600"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">${activity.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
                        <p class="text-xs text-gray-600">${new Date(activity.timestamp).toLocaleString()}</p>
                        ${activity.details && Object.keys(activity.details).length > 0 ?
                            `<p class="text-xs text-gray-500 mt-1">${JSON.stringify(activity.details)}</p>` : ''}
                    </div>
                </div>
            `).join('');

            modal.classList.remove('hidden');
            logActivity('view_activity_log');
        }

        function closeActivityLog() {
            document.getElementById('activity-log-modal').classList.add('hidden');
        }

        // --- Cart Management Functions ---
        function loadSavedCarts() {
            const savedCarts = JSON.parse(localStorage.getItem('savedCarts') || '[]');
            const container = document.getElementById('cart-list-container');

            if (savedCarts.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <i data-lucide="shopping-cart" class="w-16 h-16 mx-auto mb-4"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No Saved Carts</h3>
                        <p class="text-gray-600 mb-4">You haven't saved any carts yet. Add items to cart and save them to see them here.</p>
                        <a href="HomePage.html" class="bg-yellow-500 text-white px-6 py-2 rounded-lg hover:bg-yellow-600 transition-colors font-medium inline-flex items-center gap-2">
                            <i data-lucide="arrow-left" class="w-4 h-4"></i>
                            Start Shopping
                        </a>
                    </div>
                `;
                lucide.createIcons();
                updateCartStatistics(savedCarts);
                return;
            }

            container.innerHTML = savedCarts.map((cart, index) => `
                <div class="border border-gray-200 rounded-lg mb-6 overflow-hidden">
                    <!-- Cart Header -->
                    <div class="bg-gray-50 p-4 border-b">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-semibold text-gray-900">Cart #: ${cart.cartNumber}</h4>
                                <p class="text-sm text-gray-600">Created: ${cart.createdDate} | Customer: ${cart.customerCode}</p>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="px-3 py-1 rounded-full text-xs font-medium ${cart.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}">
                                    ${cart.status}
                                </span>
                                <button onclick="deleteCart('${cart.id}')" class="text-red-600 hover:text-red-800 p-1 rounded" title="Delete Cart">
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Cart Items Table -->
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="text-center p-2 font-semibold text-gray-700 border-r">Sl No</th>
                                    <th class="text-left p-2 font-semibold text-gray-700 border-r">Parts #</th>
                                    <th class="text-left p-2 font-semibold text-gray-700 border-r">Parts Description</th>
                                    <th class="text-center p-2 font-semibold text-gray-700 border-r">MOQ</th>
                                    <th class="text-center p-2 font-semibold text-gray-700 border-r">LOT_Size</th>
                                    <th class="text-right p-2 font-semibold text-gray-700 border-r">Unit Price</th>
                                    <th class="text-center p-2 font-semibold text-gray-700 border-r">GST</th>
                                    <th class="text-center p-2 font-semibold text-gray-700 border-r">Order Quantity</th>
                                    <th class="text-right p-2 font-semibold text-gray-700 border-r">Total Amount</th>
                                    <th class="text-right p-2 font-semibold text-gray-700 border-r">GST Amount</th>
                                    <th class="text-right p-2 font-semibold text-gray-700">Line Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${cart.items.map(item => `
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="text-center p-2 border-r">${item.slNo}</td>
                                        <td class="text-left p-2 border-r font-medium text-yellow-600">${item.partNumber}</td>
                                        <td class="text-left p-2 border-r">${item.description}</td>
                                        <td class="text-center p-2 border-r">${item.moq}</td>
                                        <td class="text-center p-2 border-r">${item.lotSize}</td>
                                        <td class="text-right p-2 border-r">${item.unitPrice}</td>
                                        <td class="text-center p-2 border-r">${item.gst}</td>
                                        <td class="text-center p-2 border-r">${item.quantity}</td>
                                        <td class="text-right p-2 border-r">${item.totalAmount}</td>
                                        <td class="text-right p-2 border-r">${item.gstAmount}</td>
                                        <td class="text-right p-2 font-semibold">${item.lineTotal}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <!-- Cart Footer -->
                    <div class="bg-gray-50 p-4 border-t">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-600">
                                No. Of Items: <span class="font-semibold">${cart.items.length}</span> |
                                Total: <span class="font-semibold text-lg text-green-600">${cart.totals.total}</span>
                            </div>
                            <div class="flex gap-2">
                                <button onclick="placeOrder('${cart.id}')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center gap-2">
                                    <i data-lucide="check-circle" class="w-4 h-4"></i>
                                    Place Order
                                </button>
                                <button onclick="editCart('${cart.id}')" class="bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors font-medium flex items-center gap-2">
                                    <i data-lucide="edit" class="w-4 h-4"></i>
                                    Edit Cart
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            lucide.createIcons();
            updateCartStatistics(savedCarts);
        }

        function updateCartStatistics(carts) {
            const totalCarts = carts.length;
            const totalItems = carts.reduce((sum, cart) => sum + cart.items.length, 0);
            const totalValue = carts.reduce((sum, cart) => {
                const value = parseFloat(cart.totals.total.replace('₹', '').replace(',', '')) || 0;
                return sum + value;
            }, 0);
            const pendingCarts = carts.filter(cart => cart.status === 'Pending').length;

            document.getElementById('total-carts').textContent = totalCarts;
            document.getElementById('total-items').textContent = totalItems;
            document.getElementById('total-value').textContent = `₹${totalValue.toFixed(2)}`;
            document.getElementById('pending-carts').textContent = pendingCarts;

            // Update cart count in sidebar
            document.getElementById('cart-count').textContent = totalCarts;
        }

        function deleteCart(cartId) {
            if (!confirm('Are you sure you want to delete this cart?')) {
                return;
            }

            const savedCarts = JSON.parse(localStorage.getItem('savedCarts') || '[]');
            const updatedCarts = savedCarts.filter(cart => cart.id !== cartId);
            localStorage.setItem('savedCarts', JSON.stringify(updatedCarts));

            loadSavedCarts();
            showToast('Cart deleted successfully', 'success');
            logActivity('cart_deleted', { cartId });
        }

        function clearAllCarts() {
            if (!confirm('Are you sure you want to delete all saved carts? This action cannot be undone.')) {
                return;
            }

            localStorage.removeItem('savedCarts');
            loadSavedCarts();
            showToast('All carts cleared successfully', 'success');
            logActivity('all_carts_cleared');
        }

        function placeOrder(cartId) {
            const savedCarts = JSON.parse(localStorage.getItem('savedCarts') || '[]');
            const cart = savedCarts.find(c => c.id === cartId);

            if (!cart) {
                showToast('Cart not found', 'error');
                return;
            }

            // Update cart status to placed
            cart.status = 'Placed';
            cart.orderDate = new Date().toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });

            localStorage.setItem('savedCarts', JSON.stringify(savedCarts));
            loadSavedCarts();

            showToast(`Order placed successfully for Cart #${cart.cartNumber}`, 'success');
            logActivity('order_placed', {
                cartId: cart.id,
                cartNumber: cart.cartNumber,
                total: cart.totals.total
            });
        }

        function editCart(cartId) {
            showToast('Edit cart functionality coming soon!', 'info');
            logActivity('cart_edit_requested', { cartId });
        }

        // --- Page Initialization ---
        document.addEventListener('DOMContentLoaded', function() {
            // Log page load
            logActivity('cart_page_load');

            // Load saved carts
            loadSavedCarts();

            // Initialize recent activity
            updateRecentActivity();

            // Initialize cart count
            const savedCarts = JSON.parse(localStorage.getItem('savedCarts') || '[]');
            document.getElementById('cart-count').textContent = savedCarts.length;
        });
    </script>
</body>
</html>
