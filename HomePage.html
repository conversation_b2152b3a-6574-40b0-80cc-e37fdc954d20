<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Lohia Corp Order Management System">
    <meta name="theme-color" content="#F1C40F">
    <title>Order Management System - Lohia Corp</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.tailwindcss.com">

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-in': 'slideIn 0.3s ease-out',
                        'bounce-subtle': 'bounceSubtle 0.6s ease-in-out',
                    }
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Enhanced icon styling */
        .lucide {
            width: 1.25rem;
            height: 1.25rem;
            stroke-width: 2px;
            flex-shrink: 0;
            transition: all 0.2s ease-in-out;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #f1f5f9; border-radius: 4px; }
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
            transition: background 0.2s ease;
        }
        ::-webkit-scrollbar-thumb:hover { background: #94a3b8; }

        /* Modal animations */
        #ai-modal-backdrop {
            transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(4px);
        }
        #ai-modal-panel {
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        /* Loading spinner */
        .spinner {
            animation: spin 1s linear infinite;
            filter: drop-shadow(0 0 6px rgba(234, 179, 8, 0.3));
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Sidebar and main content transitions */
        #sidebar, #main-content {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Mobile overlay */
        .mobile-overlay {
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            transition: opacity 0.3s ease-in-out;
        }

        /* Enhanced focus states */
        .focus-ring:focus {
            outline: none;
            box-shadow: 0 0 0 3px #ffcc024d;
        }

        /* Button hover effects */
        .btn-primary {
            background: #f2b90c;
            transition: all 0.2s ease-in-out;
        }
        .btn-primary:hover {
            background: #d4a00a;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px #ffcc024d;
        }

        /* Card hover effects */
        .card-hover {
            transition: all 0.3s ease-in-out;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Custom animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }

        @keyframes bounceSubtle {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-4px); }
        }

        /* Improved form styling */
        .form-input {
            transition: all 0.2s ease-in-out;
            border: 2px solid transparent;
        }
        .form-input:focus {
            border-color: #f2b90c;
            box-shadow: 0 0 0 3px #ffcc024d;
        }

        /* Toast notifications */
        .toast {
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        .toast.show {
            transform: translateX(0);
        }

        /* Floating Action Button Styles */
        #floating-actions {
            z-index: 1000;
        }

        #floating-actions button {
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        #floating-actions button:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        }

        /* Task completion animation */
        .task-item.completed {
            transition: all 0.3s ease;
            opacity: 0.6;
        }

        /* Quick action button hover effects */
        .quick-action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* Searchable dropdown styles */
        .searchable-dropdown .dropdown-menu {
            z-index: 1000;
            border: 1px solid #e5e7eb;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(8px);
        }

        .searchable-dropdown .dropdown-option:hover {
            background-color: #f3f4f6;
            transition: background-color 0.15s ease;
        }

        .searchable-dropdown .dropdown-option.bg-gray-200 {
            background-color: #e5e7eb;
        }

        .searchable-dropdown .dropdown-search {
            border-radius: inherit;
        }

        .searchable-dropdown .dropdown-search:focus {
            border-color: #f2b90c;
            box-shadow: 0 0 0 3px #ffcc024d;
        }

        .catalog-suggestion:hover {
            background-color: #f3f4f6;
            transition: background-color 0.15s ease;
        }

        .catalog-suggestion.bg-gray-200 {
            background-color: #e5e7eb;
        }

        /* Compact dropdown for pagination */
        .compact-dropdown .dropdown-menu {
            min-width: 120px;
        }

        .compact-dropdown .dropdown-search {
            padding: 8px 12px;
            font-size: 0.875rem;
        }

        .compact-dropdown .dropdown-option {
            padding: 8px 12px;
            font-size: 0.875rem;
        }

        /* Active dropdown state */
        .searchable-dropdown.active {
            transform: scale(1.02);
            transition: transform 0.2s ease;
        }

        /* Enhanced focus states */
        .searchable-dropdown .dropdown-search:focus {
            outline: none;
            border-color: #f2b90c;
            box-shadow: 0 0 0 3px #ffcc024d;
        }

        /* Dropdown animation */
        .dropdown-menu {
            transition: opacity 0.2s ease, transform 0.2s ease;
            transform-origin: top;
        }

        .dropdown-menu.hidden {
            opacity: 0;
            transform: scaleY(0.95);
        }

        .dropdown-menu:not(.hidden) {
            opacity: 1;
            transform: scaleY(1);
        }

        /* Search highlight */
        .dropdown-option.highlighted {
            background-color: #ffcc024d;
            border-left: 3px solid #f2b90c;
        }

        /* Animation for dynamic content */
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 font-inter">

    <!-- Mobile Overlay -->
    <div id="mobile-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden mobile-overlay"></div>

    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <div class="flex h-screen bg-white">
        <!-- Sidebar Navigation -->
        <aside id="sidebar" class="w-20 lg:w-20 bg-gray-800 text-gray-200 flex flex-col fixed inset-y-0 left-0 z-50 lg:z-30 overflow-hidden shadow-2xl transform -translate-x-full lg:translate-x-0">
            <!-- Mobile Close Button -->
            <button id="mobile-close-btn" class="absolute top-4 right-4 text-gray-400 hover:text-white lg:hidden focus-ring rounded-lg p-2" aria-label="Close navigation">
                <i data-lucide="x" class="w-5 h-5"></i>
            </button>

            <!-- Logo -->
            <div class="h-20 flex items-center justify-center px-4 border-b border-gray-700 shrink-0">
                <div id="logo-container" class="bg-white p-2 rounded-xl shadow-lg w-12 h-12 flex items-center justify-center transition-all duration-300 hover:shadow-xl">
                    <img src="Images/LCLLogo.png" alt="LohiaCorp Logo" class="h-8 w-auto transition-all duration-300" loading="lazy">
                </div>
            </div>

            <!-- Nav Links -->
            <nav class="flex-1 overflow-y-auto overflow-x-hidden" role="navigation" aria-label="Main navigation">
                <!-- Commonly Used Section -->
                <div class="p-4">
                    <h4 class="nav-text text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 opacity-0">Commonly Used</h4>
                    <ul class="space-y-2">
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 bg-yellow-500 text-gray-900 font-semibold rounded-xl shadow-lg transition-all duration-200" role="menuitem" aria-label="Home" aria-current="page">
                                <i data-lucide="home" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0">Home</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="bg-yellow-600 text-yellow-100 text-xs px-2 py-1 rounded-full">Most Used</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Part Finder">
                                <i data-lucide="search" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Part Finder</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="bg-blue-600 text-blue-100 text-xs px-2 py-1 rounded-full">Daily</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="Cart.html" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring relative" role="menuitem" aria-label="Shopping Cart">
                                <div class="relative">
                                    <i data-lucide="shopping-cart" class="w-5 h-5 flex-shrink-0"></i>
                                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center text-xs font-bold" id="cart-count">0</span>
                                </div>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Cart</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="bg-green-600 text-green-100 text-xs px-2 py-1 rounded-full">Active</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-2 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Track Orders">
                                <i data-lucide="truck" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Track Orders</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="bg-yellow-600 text-white text-xs px-2 py-1 rounded-full">Frequent</span>
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Recently Used Section -->
                <div class="p-4 border-t border-gray-600">
                    <h4 class="nav-text text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 opacity-0">Recently Used</h4>
                    <ul class="space-y-2">
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Part Ordering">
                                <i data-lucide="package-plus" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Part Ordering</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="text-xs text-gray-400">2h ago</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Created Orders">
                                <i data-lucide="file-check-2" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Created Orders</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="text-xs text-gray-400">1d ago</span>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Dashboard">
                                <i data-lucide="layout-dashboard" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Dashboard</span>
                                <span class="nav-text ml-auto opacity-0">
                                    <span class="text-xs text-gray-400">2d ago</span>
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Other Features Section -->
                <div class="p-4 border-t border-gray-600">
                    <h4 class="nav-text text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 opacity-0">Other Features</h4>
                    <ul class="space-y-2">
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="Closed Orders">
                                <i data-lucide="archive" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">Closed Orders</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="nav-link flex items-center gap-3 px-3 py-3 hover:bg-gray-700 rounded-xl transition-all duration-200 group focus-ring" role="menuitem" aria-label="About Us">
                                <i data-lucide="info" class="w-5 h-5 flex-shrink-0"></i>
                                <span class="nav-text whitespace-nowrap opacity-0 font-medium">About Us</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer / User Info -->
            <div class="p-4 border-t border-gray-700 shrink-0">
                <a href="#" class="flex items-center gap-3 group hover:bg-gray-700 rounded-xl p-2 transition-all duration-200 focus-ring" aria-label="User profile and logout">
                    <img src="https://placehold.co/40x40/E2E8F0/4A5568?text=K" alt="User Avatar" class="w-10 h-10 rounded-full flex-shrink-0 ring-2 ring-gray-600 group-hover:ring-yellow-500 transition-all duration-200" loading="lazy">
                    <div class="flex-1 nav-text opacity-0 transition-opacity duration-200">
                        <p class="font-semibold text-white text-sm whitespace-nowrap">Kanpur Plastipack</p>
                        <p class="text-xs text-gray-400 whitespace-nowrap">ID: 10340</p>
                    </div>
                    <i data-lucide="log-out" class="nav-text opacity-0 transition-all duration-200 group-hover:text-red-400"></i>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main id="main-content" class="flex-1 flex flex-col h-screen lg:ml-20 transition-all duration-300">
            <!-- Header -->
            <header class="h-16 lg:h-20 bg-white border-b border-gray-200 flex flex-col justify-center px-4 sm:px-6 lg:px-8 shrink-0 shadow-sm">
                <!-- Top Header Row -->
                <div class="flex items-center justify-between">
                    <!-- Mobile Menu Button & Title -->
                    <div class="flex items-center gap-4">
                        <button id="mobile-menu-btn" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 focus-ring transition-colors" aria-label="Open navigation menu">
                            <i data-lucide="menu" class="w-6 h-6 text-gray-600"></i>
                            <span class="sr-only">Menu</span>
                        </button>
                        <div>
                            <h1 class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 tracking-tight">Order Management System</h1>
                            <p class="text-xs sm:text-sm text-gray-500 hidden sm:block"></p>
                        </div>
                    </div>

                    <!-- Header Actions -->
                    <div class="flex items-center gap-2 sm:gap-4">
                        <!-- Notifications -->
                        <button class="relative p-2 rounded-lg hover:bg-gray-100 focus-ring transition-colors hidden sm:flex items-center gap-2" aria-label="View notifications">
                            <i data-lucide="bell" class="w-5 h-5 text-gray-600"></i>
                            <span class="hidden lg:inline text-sm text-gray-600">Notifications</span>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">2</span>
                        </button>

                        <!-- Activity Log -->
                        <button class="flex items-center gap-2 text-sm font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 px-3 py-2 rounded-lg transition-all duration-200 focus-ring" onclick="showActivityLog()" aria-label="View activity log">
                            <i data-lucide="activity" class="w-5 h-5" aria-hidden="true"></i>
                            <span class="hidden lg:inline">Activity Log</span>
                        </button>

                        <!-- Parts Policy -->
                        <button class="flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-3 py-2 rounded-lg transition-all duration-200 focus-ring">
                            <i data-lucide="shield-check" class="w-5 h-5"></i>
                            <span class="hidden sm:inline">Parts Policy</span>
                        </button>

                        <div class="w-px h-6 bg-gray-300 hidden md:block"></div>

                        <!-- User Info -->
                        <div class="hidden md:flex items-center gap-3">
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-700">Welcome, Kanpur Plastipack Limited</div>
                                <div class="text-xs text-gray-500"><EMAIL></div>
                            </div>
                            <button class="w-10 h-10 rounded-full bg-yellow-500 text-white flex items-center justify-center focus-ring font-semibold" aria-label="User profile menu">
                                K
                            </button>
                        </div>

                        <!-- Mobile User Avatar -->
                        <button class="md:hidden w-8 h-8 rounded-full bg-yellow-500 text-white flex items-center justify-center focus-ring font-semibold" aria-label="User menu">
                            K
                        </button>
                    </div>
                </div>

                <!-- Breadcrumbs -->
                <nav class="flex items-center space-x-2 text-sm text-gray-500 mt-2" aria-label="Breadcrumb">
                    <a href="#" class="flex items-center gap-1 hover:text-gray-700 transition-colors">
                        <i data-lucide="home" class="w-4 h-4"></i>
                        <span>Home</span>
                    </a>
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                    <span id="current-page" class="text-gray-900 font-medium">Parts Number Enquiry</span>
                </nav>
            </header>

            <!-- Page Content -->
            <div class="flex-1 p-2 sm:p-3 lg:p-4 overflow-y-auto bg-gray-50">
                <div class="w-full space-y-3">
                    <!-- Welcome Banner -->
                    <div class="bg-yellow-500 rounded-xl p-4 text-white shadow-xl animate-fade-in">
                        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
                            <div>
                                <h2 class="text-lg sm:text-xl font-bold mb-1">Welcome to Order Management</h2>
                                <p class="text-yellow-100 text-sm">Find parts, check prices, and manage your orders efficiently</p>
                            </div>
                            <div class="flex items-center gap-2 text-yellow-100">
                                <i data-lucide="clock" class="w-4 h-4"></i>
                                <span class="text-sm" id="current-time"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 animate-fade-in">
                        <div class="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-shadow card-hover">
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-blue-100 rounded-lg">
                                    <i data-lucide="package" class="w-5 h-5 text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-gray-900">1,247</p>
                                    <p class="text-xs text-gray-500">Available Parts</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-shadow card-hover">
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-green-100 rounded-lg">
                                    <i data-lucide="shopping-cart" class="w-5 h-5 text-green-600"></i>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-gray-900">23</p>
                                    <p class="text-xs text-gray-500">Active Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-shadow card-hover">
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-yellow-100 rounded-lg">
                                    <i data-lucide="truck" class="w-5 h-5 text-yellow-600"></i>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-gray-900">8</p>
                                    <p class="text-xs text-gray-500">In Transit</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-shadow card-hover">
                            <div class="flex items-center gap-3">
                                <div class="p-2 bg-purple-100 rounded-lg">
                                    <i data-lucide="check-circle" class="w-5 h-5 text-purple-600"></i>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-gray-900">156</p>
                                    <p class="text-xs text-gray-500">Completed</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions & Today's Tasks Row -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <!-- Quick Actions -->
                        <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center gap-2">
                                    <i data-lucide="zap" class="w-5 h-5 text-yellow-500" aria-hidden="true"></i>
                                    Quick Actions
                                </h3>
                                <button class="text-sm text-blue-600 hover:text-blue-800 font-medium focus-ring rounded-lg px-2 py-1" onclick="customizeQuickActions()">Customize</button>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <button class="quick-action-btn p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors text-left border border-blue-200" onclick="quickAction('search')" data-action="search">
                                    <div class="flex items-center gap-2 mb-1">
                                        <i data-lucide="search" class="w-4 h-4 text-blue-600" aria-hidden="true"></i>
                                        <span class="font-medium text-blue-900">Quick Search</span>
                                    </div>
                                    <p class="text-xs text-blue-700">Find parts instantly</p>
                                </button>
                                <button class="quick-action-btn p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors text-left border border-green-200" onclick="quickAction('newOrder')" data-action="newOrder">
                                    <div class="flex items-center gap-2 mb-1">
                                        <i data-lucide="plus-circle" class="w-4 h-4 text-green-600" aria-hidden="true"></i>
                                        <span class="font-medium text-green-900">New Order</span>
                                    </div>
                                    <p class="text-xs text-green-700">Create new order</p>
                                </button>
                                <button class="quick-action-btn p-3 bg-yellow-50 hover:bg-yellow-100 rounded-lg transition-colors text-left border border-yellow-200" onclick="quickAction('trackOrder')" data-action="trackOrder">
                                    <div class="flex items-center gap-2 mb-1">
                                        <i data-lucide="truck" class="w-4 h-4 text-yellow-600" aria-hidden="true"></i>
                                        <span class="font-medium text-yellow-900">Track Order</span>
                                    </div>
                                    <p class="text-xs text-yellow-700">Check order status</p>
                                </button>
                                <button class="quick-action-btn p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors text-left border border-purple-200" onclick="quickAction('aiAssist')" data-action="aiAssist">
                                    <div class="flex items-center gap-2 mb-1">
                                        <i data-lucide="brain" class="w-4 h-4 text-purple-600" aria-hidden="true"></i>
                                        <span class="font-medium text-purple-900">AI Assistant</span>
                                    </div>
                                    <p class="text-xs text-purple-700">Get smart help</p>
                                </button>
                            </div>
                        </div>

                        <!-- Today's Tasks -->
                        <div class="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center gap-2">
                                    <i data-lucide="calendar-check" class="w-5 h-5 text-green-500" aria-hidden="true"></i>
                                    Today's Tasks
                                </h3>
                                <button class="text-sm text-blue-600 hover:text-blue-800 font-medium focus-ring rounded-lg px-2 py-1" onclick="viewAllTasks()">View All</button>
                            </div>
                            <div class="space-y-3" id="todays-tasks">
                                <!-- Dynamic tasks will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Search Forms Section -->
                    <div class="bg-white rounded-2xl shadow-xl overflow-hidden card-hover">
                        <!-- Tab Navigation -->
                        <div class="border-b border-gray-200 bg-gray-50">
                            <nav class="flex overflow-x-auto" id="search-tabs" role="tablist">
                                <button data-tab="numberEnquiry" class="tab-button flex-shrink-0 text-sm sm:text-base font-semibold text-yellow-600 border-b-2 border-yellow-500 py-4 px-6 hover:bg-white transition-colors focus-ring" role="tab" aria-selected="true" aria-label="Parts Number Enquiry Tab">
                                    <span class="flex items-center gap-2">
                                        <i data-lucide="search" class="w-5 h-5 flex-shrink-0" aria-hidden="true"></i>
                                        <span class="whitespace-nowrap">Parts Number Enquiry</span>
                                    </span>
                                </button>
                                <button data-tab="priceEnquiry" class="tab-button flex-shrink-0 text-sm sm:text-base font-semibold text-gray-500 hover:text-gray-700 hover:bg-white py-4 px-6 transition-colors focus-ring" role="tab" aria-selected="false" aria-label="Price Enquiry Tab">
                                    <span class="flex items-center gap-2">
                                        <i data-lucide="dollar-sign" class="w-5 h-5 flex-shrink-0" aria-hidden="true"></i>
                                        <span class="whitespace-nowrap">Price Enquiry</span>
                                    </span>
                                </button>
                                <button data-tab="aiSearch" class="tab-button flex-shrink-0 text-sm sm:text-base font-semibold text-gray-500 hover:text-gray-700 hover:bg-white py-4 px-6 transition-colors focus-ring" role="tab" aria-selected="false" aria-label="AI-Powered Search Tab">
                                    <span class="flex items-center gap-2">
                                        <i data-lucide="sparkles" class="w-5 h-5 flex-shrink-0" aria-hidden="true"></i>
                                        <span class="hidden sm:inline whitespace-nowrap">AI-Powered Search</span>
                                        <span class="sm:hidden whitespace-nowrap">AI Search</span>
                                    </span>
                                </button>
                            </nav>
                        </div>

                        <!-- Tab Content -->
                        <div id="form-container" class="p-6">
                            <!-- Parts Number Enquiry Tab -->
                            <div id="numberEnquiry" class="tab-content space-y-8" role="tabpanel">
                                <div class="grid grid-cols-1 xl:grid-cols-2 gap-8">
                                    <!-- Catalog Search Form -->
                                    <form class="p-6 border-2 border-gray-100 rounded-2xl space-y-6 hover:border-yellow-200 transition-colors card-hover">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="p-2 bg-yellow-100 rounded-lg">
                                                <i data-lucide="book-open" class="w-5 h-5 text-yellow-600"></i>
                                            </div>
                                            <h3 class="text-lg font-semibold text-gray-800">Part Search by Catalog</h3>
                                        </div>
                                        <div id="catalogNumber-container">
                                            <label for="catalogNumber" class="block text-sm font-medium text-gray-700 mb-2">Catalog Number</label>
                                            <div class="relative">
                                                <input type="text" id="catalogNumber" placeholder="Enter or search catalog number..." class="form-input w-full pl-4 pr-12 py-3 border-2 border-gray-200 rounded-xl focus-ring transition-all" aria-describedby="catalog-help" autocomplete="off">
                                                <button type="button" id="catalog-search-btn" class="absolute inset-y-0 right-0 flex items-center px-4 bg-yellow-500 hover:bg-yellow-600 text-white rounded-r-xl transition-colors focus-ring">
                                                    <i data-lucide="search" class="w-5 h-5"></i>
                                                </button>
                                                <div id="catalog-suggestions" class="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-y-auto hidden">
                                                    <!-- Suggestions will be populated here -->
                                                </div>
                                            </div>
                                            <p id="catalog-help" class="text-xs text-gray-500 mt-1">Enter or search for the part catalog number</p>
                                        </div>
                                    </form>

                                    <!-- Serial Number Search Form -->
                                    <form class="p-6 border-2 border-gray-100 rounded-2xl space-y-6 hover:border-yellow-200 transition-colors card-hover">
                                        <div class="flex items-center gap-3 mb-4">
                                            <div class="p-2 bg-blue-100 rounded-lg">
                                                <i data-lucide="settings" class="w-5 h-5 text-blue-600"></i>
                                            </div>
                                            <h3 class="text-lg font-semibold text-gray-800">Part Search by M/C Serial No and Model</h3>
                                        </div>
                                        <div class="space-y-4">
                                            <div id="mfgYear-container">
                                                <label for="mfgYear" class="block text-sm font-medium text-gray-700 mb-2">Manufacturing Year</label>
                                                <select id="mfgYear" class="form-input w-full p-3 border-2 border-gray-200 rounded-xl focus-ring transition-all">
                                                    <option value="">--Select Year--</option>
                                                </select>
                                            </div>
                                            <div id="modelDesc-container">
                                                <label for="modelDesc" class="block text-sm font-medium text-gray-700 mb-2">Model Description</label>
                                                <select id="modelDesc" class="form-input w-full p-3 border-2 border-gray-200 rounded-xl focus-ring transition-all">
                                                    <option value="">--Select Model--</option>
                                                </select>
                                            </div>
                                            <div id="serialNumber-container">
                                                <label for="serialNumber" class="block text-sm font-medium text-gray-700 mb-2">Serial Number</label>
                                                <div class="relative">
                                                    <select id="serialNumber" class="form-input w-full p-3 pr-12 border-2 border-gray-200 rounded-xl focus-ring transition-all appearance-none">
                                                        <option value="">--Select Serial Number--</option>
                                                    </select>
                                                    <button type="button" id="serial-search-btn" class="absolute inset-y-0 right-0 flex items-center px-4 bg-blue-500 hover:bg-blue-600 text-white rounded-r-xl transition-colors focus-ring">
                                                        <i data-lucide="search" class="w-5 h-5"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Price Enquiry Tab -->
                            <div id="priceEnquiry" class="tab-content hidden" role="tabpanel">
                                <div class="max-w-2xl mx-auto">
                                    <form id="priceEnquiryForm" class="p-8 border-2 border-gray-100 rounded-2xl space-y-6 hover:border-yellow-200 transition-colors card-hover">
                                        <div class="flex items-center gap-3 mb-6">
                                            <div class="p-3 bg-green-100 rounded-xl">
                                                <i data-lucide="dollar-sign" class="w-6 h-6 text-green-600"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-xl font-semibold text-gray-800">Price Enquiry</h3>
                                                <p class="text-sm text-gray-600">Get instant pricing for any part number</p>
                                            </div>
                                        </div>

                                        <div>
                                            <label for="partNumber" class="block text-sm font-medium text-gray-700 mb-2">Part Number</label>
                                            <div class="relative">
                                                <input type="text" id="partNumber" placeholder="Enter Part Number (e.g., LP-2024-001)" class="form-input w-full pl-4 pr-12 py-4 border-2 border-gray-200 rounded-xl text-lg focus-ring transition-all" aria-describedby="part-help">
                                                <button type="button" class="absolute inset-y-0 right-0 flex items-center px-4 bg-gray-100 hover:bg-gray-200 rounded-r-xl transition-colors focus-ring">
                                                    <i data-lucide="search" class="w-5 h-5 text-gray-600"></i>
                                                </button>
                                            </div>
                                            <p id="part-help" class="text-xs text-gray-500 mt-2">Enter the exact part number to get pricing information</p>
                                        </div>

                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <button type="submit" class="btn-primary w-full py-4 rounded-xl font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-200 focus-ring flex items-center justify-center gap-2">
                                                <i data-lucide="dollar-sign" class="w-5 h-5"></i>
                                                Check Price
                                            </button>
                                            <button id="getAiAnalysis" type="button" class="w-full bg-blue-600 text-white font-semibold py-4 rounded-xl hover:bg-blue-700 focus-ring transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center gap-2" aria-label="Get AI analysis of part">
                                                <i data-lucide="brain" class="w-5 h-5 flex-shrink-0" aria-hidden="true"></i>
                                                <span>Get AI Analysis</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- AI Search Tab -->
                            <div id="aiSearch" class="tab-content hidden" role="tabpanel">
                                <div class="max-w-3xl mx-auto">
                                    <form id="aiSearchForm" class="p-8 border-2 border-gray-100 rounded-2xl space-y-6 hover:border-blue-200 transition-colors card-hover">
                                        <div class="flex items-center gap-3 mb-6">
                                            <div class="p-3 bg-purple-100 rounded-xl">
                                                <span class="text-2xl">✨</span>
                                            </div>
                                            <div>
                                                <h3 class="text-xl font-semibold text-gray-800">AI-Powered Part Search</h3>
                                                <p class="text-sm text-gray-600">Describe what you need and let AI find the right part</p>
                                            </div>
                                        </div>

                                        <div>
                                            <label for="partDescription" class="block text-sm font-medium text-gray-700 mb-2">Describe the part you need</label>
                                            <textarea id="partDescription" rows="5" placeholder="e.g., 'A reinforced drive belt for the main assembly of a 2023 Model X weaver that can handle high tension loads...'" class="form-input w-full p-4 border-2 border-gray-200 rounded-xl resize-none focus-ring transition-all" aria-describedby="description-help"></textarea>
                                            <p id="description-help" class="text-xs text-gray-500 mt-2">Be as specific as possible about the part's function, location, and requirements</p>
                                        </div>

                                        <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                                            <h4 class="font-medium text-blue-900 mb-2">💡 Tips for better results:</h4>
                                            <ul class="text-sm text-blue-800 space-y-1">
                                                <li>• Include the machine model and year</li>
                                                <li>• Mention the part's function or location</li>
                                                <li>• Describe any specific requirements (size, material, etc.)</li>
                                                <li>• Include any part numbers you might know</li>
                                            </ul>
                                        </div>

                                        <button type="submit" class="w-full bg-purple-600 text-white font-semibold py-4 rounded-xl hover:bg-purple-700 focus-ring transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center gap-2" aria-label="Find part number using AI">
                                            <i data-lucide="sparkles" class="w-5 h-5 flex-shrink-0" aria-hidden="true"></i>
                                            <span>Find Part Number with AI</span>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search Results Grid -->
                    <div id="search-results-container" class="bg-white rounded-2xl shadow-xl overflow-hidden card-hover hidden">
                        <div class="bg-yellow-500 text-white p-4">
                            <h3 class="text-lg font-semibold">Search Results</h3>
                            <p class="text-sm text-yellow-100">Found <span id="results-count">0</span> matching parts</p>
                        </div>

                        <!-- Results Table -->
                        <div class="border border-gray-300">
                            <table class="w-full text-xs border-collapse table-fixed">
                                <thead class="bg-gray-100">
                                    <tr class="border-b-2 border-gray-300">
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-12">Select</th>
                                        <th class="text-left p-1 font-semibold text-gray-700 border-r border-gray-300 w-20">Part Number</th>
                                        <th class="text-left p-1 font-semibold text-gray-700 border-r border-gray-300 w-32">Part Description</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-16">HSN</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-16">MOQ</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-16">Lot Size</th>
                                        <th class="text-right p-1 font-semibold text-gray-700 border-r border-gray-300 w-24">Unit Price</th>
                                        <th class="text-right p-1 font-semibold text-gray-700 border-r border-gray-300 w-20">SP/MOQ</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-12">CGST%</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-12">UOM</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-16">Sales Org</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-16">Dist Channel</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 border-r border-gray-300 w-12">Plant</th>
                                        <th class="text-center p-1 font-semibold text-gray-700 w-16">Commodity Code</th>
                                    </tr>
                                </thead>
                                <tbody id="search-results-tbody" class="bg-white">
                                    <!-- Results will be populated here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="bg-gray-50 px-4 py-3 flex items-center justify-between border-t">
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-gray-700">Show</span>
                                <div id="results-per-page-container" class="inline-block">
                                    <select id="results-per-page" class="border border-gray-300 rounded px-2 py-1 text-sm">
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>
                                <span class="text-sm text-gray-700">entries</span>
                            </div>

                            <div class="flex items-center gap-2">
                                <button id="prev-page" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 disabled:opacity-50" disabled>Previous</button>
                                <span id="page-info" class="text-sm text-gray-700">Page 1 of 1</span>
                                <button id="next-page" class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100 disabled:opacity-50" disabled>Next</button>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="bg-gray-100 p-4 flex flex-wrap gap-3">
                            <button id="proceed-btn" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 flex items-center gap-2" disabled aria-label="Proceed with selected items">
                                <i data-lucide="arrow-right" class="w-4 h-4" aria-hidden="true"></i>
                                <span>Proceed</span>
                            </button>
                            <button id="add-to-cart-btn" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 flex items-center gap-2" disabled aria-label="Add selected items to cart">
                                <i data-lucide="shopping-cart" class="w-4 h-4" aria-hidden="true"></i>
                                <span>Add To Cart</span>
                            </button>
                            <button id="clear-selection-btn" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors font-medium flex items-center gap-2" aria-label="Clear all selections">
                                <i data-lucide="x-circle" class="w-4 h-4" aria-hidden="true"></i>
                                <span>Clear Selection</span>
                            </button>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div id="recent-activity-section" class="bg-white rounded-2xl shadow-xl p-6 card-hover">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-800">Recent Activity</h3>
                            <button class="text-sm text-blue-600 hover:text-blue-800 font-medium focus-ring rounded-lg px-3 py-1">View All</button>
                        </div>
                        <div class="space-y-4" id="recent-activity-container">
                            <!-- Dynamic recent activity will be populated here -->
                        </div>
                    </div>


                </div>
            </div>
        </main>
    </div>

    <!-- Floating Action Buttons -->
    <div class="fixed bottom-6 right-6 flex flex-col gap-3 z-40" id="floating-actions">
        <!-- Main FAB -->
        <button id="main-fab" class="w-14 h-14 bg-yellow-500 hover:bg-yellow-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group" onclick="toggleFloatingMenu()" aria-label="Quick actions menu">
            <i data-lucide="plus" class="w-6 h-6 transition-transform duration-300 group-hover:rotate-45" aria-hidden="true"></i>
        </button>

        <!-- Secondary FABs -->
        <div id="fab-menu" class="flex flex-col gap-3 opacity-0 scale-0 transition-all duration-300 origin-bottom">
            <button class="w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" onclick="floatingAction('search')" aria-label="Quick search">
                <i data-lucide="search" class="w-5 h-5" aria-hidden="true"></i>
            </button>
            <button class="w-12 h-12 bg-green-600 hover:bg-green-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" onclick="floatingAction('newOrder')" aria-label="New order">
                <i data-lucide="plus-circle" class="w-5 h-5" aria-hidden="true"></i>
            </button>
            <button class="w-12 h-12 bg-yellow-600 hover:bg-yellow-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" onclick="floatingAction('track')" aria-label="Track order">
                <i data-lucide="truck" class="w-5 h-5" aria-hidden="true"></i>
            </button>
            <button class="w-12 h-12 bg-purple-600 hover:bg-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center" onclick="floatingAction('ai')" aria-label="AI assistant">
                <i data-lucide="brain" class="w-5 h-5" aria-hidden="true"></i>
            </button>
        </div>
    </div>

    <!-- Activity Log Modal -->
    <div id="activity-log-modal" class="fixed inset-0 z-50 hidden" role="dialog" aria-modal="true" aria-labelledby="activity-log-title">
        <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300" onclick="closeActivityLog()"></div>
        <div class="relative flex items-center justify-center min-h-screen p-4">
            <div class="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-2xl shadow-2xl transform transition-all duration-300 overflow-hidden">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-blue-50">
                    <div class="flex items-center gap-3">
                        <div class="p-3 bg-blue-100 rounded-lg">
                            <i data-lucide="activity" class="w-6 h-6 text-blue-600" aria-hidden="true"></i>
                        </div>
                        <div>
                            <h3 id="activity-log-title" class="text-xl font-semibold text-gray-900">Activity Log</h3>
                            <p class="text-sm text-gray-600">Complete history of user actions</p>
                        </div>
                    </div>
                    <button onclick="closeActivityLog()" class="p-2 text-gray-500 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors focus-ring" aria-label="Close activity log">
                        <i data-lucide="x" class="w-5 h-5" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Modal Content -->
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <div id="activity-log-content" class="space-y-4">
                        <!-- Activity items will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cart Details Modal -->
    <div id="cart-modal" class="fixed inset-0 z-50 hidden" role="dialog" aria-modal="true" aria-labelledby="cart-modal-title">
        <div class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300" onclick="closeCartModal()"></div>
        <div class="relative flex items-center justify-center min-h-screen p-4">
            <div class="relative w-full max-w-6xl max-h-[90vh] bg-white rounded-2xl shadow-2xl transform transition-all duration-300 overflow-hidden">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-blue-50">
                    <div class="flex items-center gap-3">
                        <div class="p-3 bg-blue-100 rounded-lg">
                            <i data-lucide="shopping-cart" class="w-6 h-6 text-blue-600" aria-hidden="true"></i>
                        </div>
                        <div>
                            <h3 id="cart-modal-title" class="text-xl font-semibold text-gray-900">Parts Details</h3>
                            <p class="text-sm text-gray-600">Review and confirm your cart items</p>
                        </div>
                    </div>
                    <button onclick="closeCartModal()" class="p-2 text-gray-500 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors focus-ring" aria-label="Close cart modal">
                        <i data-lucide="x" class="w-5 h-5" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Modal Content -->
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
                    <!-- Cart Info -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
                        <div>
                            <label class="text-sm font-medium text-gray-600">Cart #:</label>
                            <p id="cart-number" class="text-lg font-semibold text-gray-900">10340/06/25/22</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-600">Customer Code:</label>
                            <p id="customer-code" class="text-lg font-semibold text-gray-900">10340</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-600">Submitted Date:</label>
                            <p id="submitted-date" class="text-lg font-semibold text-gray-900">10-Jun-2025</p>
                        </div>
                    </div>

                    <!-- Cart Items Table -->
                    <div class="border border-gray-300 rounded-lg overflow-hidden">
                        <table class="w-full text-sm border-collapse">
                            <thead class="bg-gray-100">
                                <tr class="border-b-2 border-gray-300">
                                    <th class="text-center p-3 font-semibold text-gray-700 border-r border-gray-300">Sl No</th>
                                    <th class="text-left p-3 font-semibold text-gray-700 border-r border-gray-300">Parts #</th>
                                    <th class="text-left p-3 font-semibold text-gray-700 border-r border-gray-300">Parts Description</th>
                                    <th class="text-center p-3 font-semibold text-gray-700 border-r border-gray-300">MOQ</th>
                                    <th class="text-center p-3 font-semibold text-gray-700 border-r border-gray-300">LOT_Size</th>
                                    <th class="text-right p-3 font-semibold text-gray-700 border-r border-gray-300">Unit Price</th>
                                    <th class="text-center p-3 font-semibold text-gray-700 border-r border-gray-300">GST</th>
                                    <th class="text-center p-3 font-semibold text-gray-700 border-r border-gray-300">Order Quantity</th>
                                    <th class="text-right p-3 font-semibold text-gray-700 border-r border-gray-300">Total Amount</th>
                                    <th class="text-right p-3 font-semibold text-gray-700 border-r border-gray-300">GST Amount</th>
                                    <th class="text-right p-3 font-semibold text-gray-700">Line Total</th>
                                </tr>
                            </thead>
                            <tbody id="cart-items-tbody" class="bg-white">
                                <!-- Cart items will be populated here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Cart Summary -->
                    <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-3">
                            <h4 class="font-semibold text-gray-900">Order Summary</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Subtotal:</span>
                                    <span id="cart-subtotal" class="font-medium">₹0.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">GST:</span>
                                    <span id="cart-gst" class="font-medium">₹0.00</span>
                                </div>
                                <div class="flex justify-between border-t pt-2">
                                    <span class="font-semibold text-gray-900">Total:</span>
                                    <span id="cart-total" class="font-semibold text-lg text-blue-600">₹0.00</span>
                                </div>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <h4 class="font-semibold text-gray-900">Delivery Information</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Estimated Delivery:</span>
                                    <span class="font-medium">5-7 business days</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Shipping Method:</span>
                                    <span class="font-medium">Standard Delivery</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="border-t border-gray-200 p-6 bg-gray-50">
                    <div class="flex flex-col sm:flex-row gap-3 justify-end">
                        <button onclick="closeCartModal()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors font-medium">
                            Cancel
                        </button>
                        <button onclick="proceedToCheckout()" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center gap-2">
                            <i data-lucide="credit-card" class="w-4 h-4"></i>
                            Proceed to Checkout
                        </button>
                        <button onclick="saveCart()" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center gap-2">
                            <i data-lucide="save" class="w-4 h-4"></i>
                            Save Cart
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Results Modal -->
    <div id="ai-modal" class="fixed inset-0 z-50 hidden" role="dialog" aria-modal="true" aria-labelledby="ai-modal-title">
        <div id="ai-modal-backdrop" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300"></div>
        <div class="relative flex items-center justify-center min-h-screen p-4">
            <div id="ai-modal-panel" class="relative w-full max-w-4xl max-h-[90vh] bg-white rounded-2xl shadow-2xl transform transition-all duration-300 opacity-0 scale-95 overflow-hidden">
                <!-- Modal Header -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-yellow-50">
                    <div class="flex items-center gap-3">
                        <div class="p-3 bg-yellow-100 rounded-lg">
                            <i data-lucide="brain" class="w-6 h-6 text-yellow-600" aria-hidden="true"></i>
                        </div>
                        <div>
                            <h3 id="ai-modal-title" class="text-xl font-semibold text-gray-900">AI Assistant</h3>
                            <p class="text-sm text-gray-600">Intelligent part analysis and recommendations</p>
                        </div>
                    </div>
                    <button id="close-ai-modal" class="p-2 text-gray-500 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors focus-ring" aria-label="Close AI Assistant modal">
                        <i data-lucide="x" class="w-5 h-5" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Modal Content -->
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <!-- Loading State -->
                    <div id="ai-modal-loading" class="text-center py-16 hidden">
                        <div class="spinner w-16 h-16 border-4 border-yellow-500 border-t-transparent rounded-full mx-auto mb-6"></div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2">AI is analyzing...</h4>
                        <p class="text-gray-600">Please wait while we process your request</p>
                        <div class="mt-4 flex justify-center">
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-yellow-500 rounded-full animate-bounce"></div>
                                <div class="w-2 h-2 bg-yellow-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                <div class="w-2 h-2 bg-yellow-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    <div id="ai-modal-content" class="prose prose-lg max-w-none"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // --- Global Variables ---
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const navTexts = document.querySelectorAll('.nav-text');
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileCloseBtn = document.getElementById('mobile-close-btn');
        const mobileOverlay = document.getElementById('mobile-overlay');
        const toastContainer = document.getElementById('toast-container');

        // --- Utility Functions ---
        function showToast(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            const bgColor = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-blue-500'
            }[type] || 'bg-blue-500';

            toast.className = `toast ${bgColor} text-white px-6 py-4 rounded-lg shadow-lg flex items-center gap-3 mb-2`;
            toast.innerHTML = `
                <i data-lucide="${type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : type === 'warning' ? 'alert-triangle' : 'info'}" class="w-5 h-5"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.remove()" class="ml-auto hover:bg-white hover:bg-opacity-20 rounded p-1">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            `;

            toastContainer.appendChild(toast);
            lucide.createIcons();

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Auto remove
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, duration);
        }

        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // --- Mobile Navigation Logic ---
        function openMobileMenu() {
            sidebar.classList.remove('-translate-x-full');
            mobileOverlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeMobileMenu() {
            sidebar.classList.add('-translate-x-full');
            mobileOverlay.classList.add('hidden');
            document.body.style.overflow = '';
        }

        // --- Desktop Sidebar Logic (Hover-based) ---
        const expandSidebar = () => {
            if (window.innerWidth >= 1024) { // Only on desktop
                sidebar.classList.remove('lg:w-20');
                sidebar.classList.add('lg:w-64');
                mainContent.classList.remove('lg:ml-20');
                mainContent.classList.add('lg:ml-64');
                navTexts.forEach(text => {
                    text.classList.remove('opacity-0');
                });
            }
        };

        const collapseSidebar = () => {
            if (window.innerWidth >= 1024) { // Only on desktop
                sidebar.classList.add('lg:w-20');
                sidebar.classList.remove('lg:w-64');
                mainContent.classList.add('lg:ml-20');
                mainContent.classList.remove('lg:ml-64');
                navTexts.forEach(text => {
                    text.classList.add('opacity-0');
                });
            }
        };

        // --- Event Listeners ---
        mobileMenuBtn?.addEventListener('click', openMobileMenu);
        mobileCloseBtn?.addEventListener('click', closeMobileMenu);
        mobileOverlay?.addEventListener('click', closeMobileMenu);

        // Desktop sidebar hover
        sidebar.addEventListener('mouseenter', expandSidebar);
        sidebar.addEventListener('mouseleave', collapseSidebar);

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 1024) {
                closeMobileMenu();
            }
        });

        // Update time every minute
        updateCurrentTime();
        setInterval(updateCurrentTime, 60000);

        // --- Activity Logging System ---
        let activityLog = JSON.parse(localStorage.getItem('userActivityLog') || '[]');

        function logActivity(action, details = {}) {
            const activity = {
                id: Date.now(),
                timestamp: new Date().toISOString(),
                action: action,
                details: details,
                user: 'Kanpur Plastipack Limited'
            };

            activityLog.unshift(activity);

            // Keep only last 100 activities
            if (activityLog.length > 100) {
                activityLog = activityLog.slice(0, 100);
            }

            localStorage.setItem('userActivityLog', JSON.stringify(activityLog));
            updateRecentActivity();
        }

        // --- Dynamic Recent Activity System ---
        function generateRecentActivity() {
            const activities = [];
            const now = new Date();

            // Generate 5-8 recent activities
            const activityCount = 5 + Math.floor(Math.random() * 4);

            for (let i = 0; i < activityCount; i++) {
                const activityType = dynamicData.activityTypes[Math.floor(Math.random() * dynamicData.activityTypes.length)];
                const template = activityType.templates[Math.floor(Math.random() * activityType.templates.length)];

                // Generate timestamp (last 24 hours)
                const hoursAgo = Math.floor(Math.random() * 24);
                const minutesAgo = Math.floor(Math.random() * 60);
                const activityTime = new Date(now.getTime() - (hoursAgo * 60 * 60 * 1000) - (minutesAgo * 60 * 1000));

                const activity = {
                    id: `activity_${Date.now()}_${i}`,
                    type: activityType.type,
                    icon: activityType.icon,
                    color: activityType.color,
                    title: replacePlaceholders(template),
                    description: generateActivityDescription(activityType.type),
                    timestamp: activityTime,
                    timeAgo: formatTimeAgo(activityTime)
                };

                activities.push(activity);
            }

            // Sort by timestamp (newest first)
            activities.sort((a, b) => b.timestamp - a.timestamp);

            return activities;
        }

        function generateActivityDescription(activityType) {
            const descriptions = {
                order_completed: ['Drive belt assembly delivered', 'Hydraulic pump installation complete', 'Bearing set replacement finished'],
                order_shipped: ['Bearing set for Model B', 'Control valve assembly', 'Motor components package'],
                order_created: ['Hydraulic pump components', 'Drive belt replacement', 'Gear assembly order'],
                inventory_updated: ['Stock levels synchronized', 'Warehouse audit complete', 'Inventory reconciliation'],
                price_updated: ['Market rate adjustment', 'Bulk pricing revision', 'Seasonal price update'],
                customer_contacted: ['Order status inquiry', 'Technical support call', 'Delivery schedule discussion'],
                quality_check: ['QC inspection passed', 'Quality metrics reviewed', 'Compliance audit complete'],
                vendor_meeting: ['Supply chain discussion', 'Delivery schedule review', 'Quality standards meeting']
            };

            const typeDescriptions = descriptions[activityType] || ['Activity completed'];
            return typeDescriptions[Math.floor(Math.random() * typeDescriptions.length)];
        }

        function formatTimeAgo(timestamp) {
            const now = new Date();
            const diffMs = now - timestamp;
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffMinutes = Math.floor(diffMs / (1000 * 60));

            if (diffHours >= 24) {
                const diffDays = Math.floor(diffHours / 24);
                return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
            } else if (diffHours >= 1) {
                return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
            } else if (diffMinutes >= 1) {
                return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
            } else {
                return 'Just now';
            }
        }

        function renderRecentActivity() {
            const container = document.querySelector('#recent-activity-container');
            if (!container) return;

            const activities = generateRecentActivity();

            container.innerHTML = activities.slice(0, 3).map(activity => `
                <div class="flex items-center gap-4 p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
                    <div class="p-2 bg-${activity.color}-100 rounded-lg">
                        <i data-lucide="${activity.icon}" class="w-4 h-4 text-${activity.color}-600"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">${activity.title}</p>
                        <p class="text-xs text-gray-500">${activity.description}</p>
                    </div>
                    <span class="text-xs text-gray-400">${activity.timeAgo}</span>
                </div>
            `).join('');

            // Reinitialize Lucide icons
            lucide.createIcons();
        }

        function updateRecentActivity() {
            // Update recent activity section with latest logs
            const recentActivities = activityLog.slice(0, 3);
            renderRecentActivity();
        }

        // --- Floating Action Buttons ---
        let fabMenuOpen = false;

        function toggleFloatingMenu() {
            const fabMenu = document.getElementById('fab-menu');
            const mainFab = document.getElementById('main-fab');

            fabMenuOpen = !fabMenuOpen;

            if (fabMenuOpen) {
                fabMenu.classList.remove('opacity-0', 'scale-0');
                fabMenu.classList.add('opacity-100', 'scale-100');
                mainFab.querySelector('i').style.transform = 'rotate(45deg)';
            } else {
                fabMenu.classList.add('opacity-0', 'scale-0');
                fabMenu.classList.remove('opacity-100', 'scale-100');
                mainFab.querySelector('i').style.transform = 'rotate(0deg)';
            }

            logActivity('fab_menu_toggle', { opened: fabMenuOpen });
        }

        function floatingAction(action) {
            logActivity('floating_action', { action: action });

            switch(action) {
                case 'search':
                    switchTab('numberEnquiry');
                    document.getElementById('catalogNumber')?.focus();
                    showToast('Quick search activated', 'info');
                    break;
                case 'newOrder':
                    showToast('Redirecting to new order...', 'info');
                    break;
                case 'track':
                    showToast('Opening order tracking...', 'info');
                    break;
                case 'ai':
                    switchTab('aiSearch');
                    showToast('AI Assistant activated', 'info');
                    break;
            }

            toggleFloatingMenu(); // Close menu after action
        }

        // --- Quick Actions Functions ---
        function quickAction(action) {
            logActivity('quick_action', { action: action });

            switch(action) {
                case 'search':
                    switchTab('numberEnquiry');
                    document.getElementById('catalogNumber')?.focus();
                    break;
                case 'newOrder':
                    showToast('Creating new order...', 'info');
                    break;
                case 'trackOrder':
                    showToast('Opening order tracking...', 'info');
                    break;
                case 'aiAssist':
                    switchTab('aiSearch');
                    break;
            }
        }

        function customizeQuickActions() {
            logActivity('customize_quick_actions');
            showToast('Quick Actions customization coming soon!', 'info');
        }

        // --- Today's Tasks Functions ---
        function completeTask(checkbox, taskId) {
            const taskItem = checkbox.closest('.task-item');
            const today = new Date();
            const dayKey = today.toDateString();

            if (checkbox.checked) {
                taskItem.style.opacity = '0.6';
                taskItem.style.textDecoration = 'line-through';

                // Update task in localStorage
                const storedTasks = localStorage.getItem(`tasks_${dayKey}`);
                if (storedTasks) {
                    const tasks = JSON.parse(storedTasks);
                    const task = tasks.find(t => t.id === taskId);
                    if (task) {
                        task.completed = true;
                        task.completedAt = new Date().toISOString();
                        localStorage.setItem(`tasks_${dayKey}`, JSON.stringify(tasks));
                    }
                }

                logActivity('task_completed', { taskId: taskId });
                showToast('Task completed!', 'success');

                // Add completion animation
                setTimeout(() => {
                    taskItem.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        taskItem.style.transform = 'scale(1)';
                    }, 200);
                }, 100);

            } else {
                taskItem.style.opacity = '1';
                taskItem.style.textDecoration = 'none';

                // Update task in localStorage
                const storedTasks = localStorage.getItem(`tasks_${dayKey}`);
                if (storedTasks) {
                    const tasks = JSON.parse(storedTasks);
                    const task = tasks.find(t => t.id === taskId);
                    if (task) {
                        task.completed = false;
                        delete task.completedAt;
                        localStorage.setItem(`tasks_${dayKey}`, JSON.stringify(tasks));
                    }
                }

                logActivity('task_uncompleted', { taskId: taskId });
            }
        }

        function viewAllTasks() {
            logActivity('view_all_tasks');

            // Get current tasks
            const today = new Date();
            const dayKey = today.toDateString();
            const storedTasks = localStorage.getItem(`tasks_${dayKey}`);

            if (storedTasks) {
                const tasks = JSON.parse(storedTasks);
                const completedTasks = tasks.filter(t => t.completed).length;
                const totalTasks = tasks.length;
                const pendingTasks = totalTasks - completedTasks;

                showToast(`Tasks Overview: ${completedTasks}/${totalTasks} completed, ${pendingTasks} pending`, 'info', 5000);
            } else {
                showToast('No tasks found for today', 'info');
            }
        }

        // Add function to manually refresh tasks (useful for testing)
        function refreshTasksManually() {
            const today = new Date();
            const dayKey = today.toDateString();

            // Clear today's tasks to regenerate them
            localStorage.removeItem(`tasks_${dayKey}`);
            generateDynamicTasks();
            showToast('Tasks refreshed successfully!', 'success');
        }

        // --- Dynamic Task Generation System ---
        const taskTemplates = [
            {
                type: 'order_review',
                title: 'Review Order #ORD-{orderNum}',
                description: 'Verify order details and approve',
                priority: 'high',
                timeRange: [9, 16], // 9 AM to 4 PM
                probability: 0.8
            },
            {
                type: 'parts_approval',
                title: 'Approve Parts List for {customer}',
                description: 'Review and approve parts requisition',
                priority: 'medium',
                timeRange: [10, 17],
                probability: 0.6
            },
            {
                type: 'inventory_update',
                title: 'Update Inventory Status',
                description: 'Sync inventory levels with warehouse',
                priority: 'low',
                timeRange: [14, 18],
                probability: 0.9
            },
            {
                type: 'customer_followup',
                title: 'Follow up with {customer}',
                description: 'Check on order status and satisfaction',
                priority: 'medium',
                timeRange: [11, 15],
                probability: 0.5
            },
            {
                type: 'price_review',
                title: 'Review Pricing for {partType}',
                description: 'Update pricing based on market changes',
                priority: 'low',
                timeRange: [13, 17],
                probability: 0.4
            },
            {
                type: 'quality_check',
                title: 'Quality Check Report',
                description: 'Review quality metrics and reports',
                priority: 'high',
                timeRange: [9, 12],
                probability: 0.7
            },
            {
                type: 'vendor_meeting',
                title: 'Vendor Meeting - {vendor}',
                description: 'Discuss supply chain and delivery schedules',
                priority: 'medium',
                timeRange: [10, 16],
                probability: 0.3
            },
            {
                type: 'system_backup',
                title: 'System Data Backup',
                description: 'Verify daily backup completion',
                priority: 'low',
                timeRange: [17, 19],
                probability: 0.8
            }
        ];

        // --- Comprehensive Dynamic Data Sets ---
        const dynamicData = {
            orderNumbers: ['2024-156', '2024-157', '2024-158', '2024-159', '2024-160', '2024-161', '2024-162', '2024-163', '2024-164', '2024-165'],
            customers: [
                'Kanpur Plastipack Ltd', 'Delhi Textiles Co', 'Mumbai Industries', 'Chennai Weavers', 'Bangalore Mills',
                'Hyderabad Fabrics', 'Pune Textile Corp', 'Ahmedabad Looms', 'Coimbatore Spinners', 'Ludhiana Weavers',
                'Surat Silk Mills', 'Tirupur Garments', 'Erode Textiles', 'Salem Cotton Mills', 'Madurai Handlooms'
            ],
            partTypes: [
                'Drive Belts', 'Bearing Sets', 'Hydraulic Pumps', 'Control Valves', 'Motor Components',
                'Gear Assemblies', 'Coupling Units', 'Shaft Components', 'Brake Systems', 'Clutch Mechanisms',
                'Transmission Parts', 'Pneumatic Cylinders', 'Electrical Controls', 'Sensor Units', 'Filter Elements'
            ],
            vendors: [
                'Precision Parts Inc', 'Industrial Supply Co', 'Quality Components Ltd', 'Reliable Parts Corp',
                'Advanced Manufacturing', 'Elite Components', 'Superior Parts Ltd', 'Prime Industrial Supply',
                'Apex Engineering', 'Global Parts Network', 'Innovative Solutions', 'Professional Components'
            ],
            manufacturingYears: [
                { value: '2025', label: '2025' },
                { value: '2024', label: '2024' },
                { value: '2023', label: '2023' },
                { value: '2022', label: '2022' },
                { value: '2021', label: '2021' },
                { value: '2020', label: '2020' },
                { value: '2019', label: '2019' },
                { value: '2018', label: '2018' },
                { value: '2017', label: '2017' },
                { value: '2016', label: '2016' },
                { value: '2015', label: '2015' },
                { value: '2014', label: '2014' },
                { value: '2013', label: '2013' },
                { value: '2012', label: '2012' },
                { value: '2011', label: '2011' },
                { value: '2010', label: '2010' }
            ],
            modelDescriptions: [
                { value: 'lw-2000', label: 'LW-2000 - Standard Weaver', category: 'standard' },
                { value: 'lw-2500', label: 'LW-2500 - Heavy Duty Weaver', category: 'heavy-duty' },
                { value: 'lw-3000', label: 'LW-3000 - Compact Series', category: 'compact' },
                { value: 'lw-3500', label: 'LW-3500 - High Speed Weaver', category: 'high-speed' },
                { value: 'lw-4000', label: 'LW-4000 - Industrial Grade', category: 'industrial' },
                { value: 'lw-4500', label: 'LW-4500 - Premium Series', category: 'premium' },
                { value: 'lw-5000', label: 'LW-5000 - Advanced Weaver', category: 'advanced' },
                { value: 'lw-5500', label: 'LW-5500 - Professional Series', category: 'professional' },
                { value: 'lw-6000', label: 'LW-6000 - Enterprise Model', category: 'enterprise' },
                { value: 'lw-6500', label: 'LW-6500 - Ultra Performance', category: 'ultra' },
                { value: 'ls-1000', label: 'LS-1000 - Spinning Machine', category: 'spinning' },
                { value: 'ls-1500', label: 'LS-1500 - Ring Spinning Frame', category: 'spinning' },
                { value: 'lt-800', label: 'LT-800 - Texturing Machine', category: 'texturing' },
                { value: 'lt-1200', label: 'LT-1200 - Air Jet Texturing', category: 'texturing' },
                { value: 'ld-600', label: 'LD-600 - Draw Frame', category: 'drawing' }
            ],
            catalogNumbers: [
                'LP-2024-001', 'LP-2024-002', 'LP-2024-003', 'LP-2024-004', 'LP-2024-005',
                'LB-2024-101', 'LB-2024-102', 'LB-2024-103', 'LB-2024-104', 'LB-2024-105',
                'LH-2024-201', 'LH-2024-202', 'LH-2024-203', 'LH-2024-204', 'LH-2024-205',
                'LC-2024-301', 'LC-2024-302', 'LC-2024-303', 'LC-2024-304', 'LC-2024-305',
                'LM-2024-401', 'LM-2024-402', 'LM-2024-403', 'LM-2024-404', 'LM-2024-405',
                'LG-2024-501', 'LG-2024-502', 'LG-2024-503', 'LG-2024-504', 'LG-2024-505',
                'LS-2024-601', 'LS-2024-602', 'LS-2024-603', 'LS-2024-604', 'LS-2024-605',
                'LT-2024-701', 'LT-2024-702', 'LT-2024-703', 'LT-2024-704', 'LT-2024-705'
            ],
            resultsPerPageOptions: [
                { value: '5', label: '5 entries' },
                { value: '10', label: '10 entries' },
                { value: '25', label: '25 entries' },
                { value: '50', label: '50 entries' },
                { value: '100', label: '100 entries' },
                { value: '250', label: '250 entries' },
                { value: '500', label: '500 entries' }
            ],
            activityTypes: [
                { type: 'order_completed', icon: 'check-circle', color: 'green', templates: [
                    'Order #{orderNum} completed',
                    'Order #{orderNum} delivered successfully',
                    'Completed order #{orderNum} for {customer}'
                ]},
                { type: 'order_shipped', icon: 'truck', color: 'blue', templates: [
                    'Order #{orderNum} shipped',
                    'Shipment dispatched for order #{orderNum}',
                    'Order #{orderNum} out for delivery'
                ]},
                { type: 'order_created', icon: 'package-plus', color: 'yellow', templates: [
                    'New order #{orderNum} created',
                    'Order #{orderNum} received from {customer}',
                    'Created order #{orderNum} for {partType}'
                ]},
                { type: 'inventory_updated', icon: 'database', color: 'purple', templates: [
                    'Inventory updated for {partType}',
                    'Stock levels synchronized',
                    'Inventory audit completed'
                ]},
                { type: 'price_updated', icon: 'dollar-sign', color: 'green', templates: [
                    'Pricing updated for {partType}',
                    'Price revision completed',
                    'Market pricing synchronized'
                ]},
                { type: 'customer_contacted', icon: 'phone', color: 'blue', templates: [
                    'Contacted {customer}',
                    'Follow-up call with {customer}',
                    'Customer meeting scheduled with {customer}'
                ]},
                { type: 'quality_check', icon: 'shield-check', color: 'green', templates: [
                    'Quality check completed',
                    'QC report generated',
                    'Quality audit passed'
                ]},
                { type: 'vendor_meeting', icon: 'users', color: 'orange', templates: [
                    'Meeting with {vendor}',
                    'Vendor discussion completed',
                    'Supply chain review with {vendor}'
                ]}
            ]
        };

        function generateDynamicTasks() {
            const today = new Date();
            const currentHour = today.getHours();
            const dayKey = today.toDateString();

            // Check if tasks were already generated today
            const storedTasks = localStorage.getItem(`tasks_${dayKey}`);
            if (storedTasks) {
                const tasks = JSON.parse(storedTasks);
                renderTasks(tasks);
                return;
            }

            const generatedTasks = [];
            const maxTasks = 4; // Maximum tasks per day
            let taskCount = 0;

            // Shuffle task templates for variety
            const shuffledTemplates = [...taskTemplates].sort(() => Math.random() - 0.5);

            for (const template of shuffledTemplates) {
                if (taskCount >= maxTasks) break;

                // Check if task should be generated based on probability
                if (Math.random() > template.probability) continue;

                // Generate task time within the template's time range
                const startHour = template.timeRange[0];
                const endHour = template.timeRange[1];
                const taskHour = startHour + Math.floor(Math.random() * (endHour - startHour));
                const taskMinute = Math.floor(Math.random() * 60);

                // Create task object
                const task = {
                    id: `task_${Date.now()}_${taskCount}`,
                    type: template.type,
                    title: replacePlaceholders(template.title),
                    description: template.description,
                    priority: template.priority,
                    dueTime: `${taskHour.toString().padStart(2, '0')}:${taskMinute.toString().padStart(2, '0')}`,
                    dueHour: taskHour,
                    completed: false,
                    createdAt: today.toISOString()
                };

                generatedTasks.push(task);
                taskCount++;
            }

            // Sort tasks by due time
            generatedTasks.sort((a, b) => a.dueHour - b.dueHour);

            // Store tasks for the day
            localStorage.setItem(`tasks_${dayKey}`, JSON.stringify(generatedTasks));

            renderTasks(generatedTasks);
        }

        function replacePlaceholders(text) {
            return text
                .replace('{orderNum}', dynamicData.orderNumbers[Math.floor(Math.random() * dynamicData.orderNumbers.length)])
                .replace('{customer}', dynamicData.customers[Math.floor(Math.random() * dynamicData.customers.length)])
                .replace('{partType}', dynamicData.partTypes[Math.floor(Math.random() * dynamicData.partTypes.length)])
                .replace('{vendor}', dynamicData.vendors[Math.floor(Math.random() * dynamicData.vendors.length)]);
        }

        function renderTasks(tasks) {
            const container = document.getElementById('todays-tasks');
            const currentTime = new Date();
            const currentHour = currentTime.getHours();
            const currentMinute = currentTime.getMinutes();

            if (!tasks || tasks.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i data-lucide="calendar-check" class="w-12 h-12 mx-auto mb-3 text-gray-300"></i>
                        <p class="text-sm">No tasks scheduled for today</p>
                        <p class="text-xs mt-1">Enjoy your productive day!</p>
                    </div>
                `;
                lucide.createIcons();
                return;
            }

            container.innerHTML = tasks.map(task => {
                const priorityConfig = {
                    high: { bg: 'bg-red-50', border: 'border-red-200', text: 'text-red-800', badge: 'bg-red-100' },
                    medium: { bg: 'bg-yellow-50', border: 'border-yellow-200', text: 'text-yellow-800', badge: 'bg-yellow-100' },
                    low: { bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-800', badge: 'bg-green-100' }
                };

                const config = priorityConfig[task.priority] || priorityConfig.medium;

                // Check if task is overdue
                const [taskHour, taskMinute] = task.dueTime.split(':').map(Number);
                const isOverdue = (currentHour > taskHour) || (currentHour === taskHour && currentMinute > taskMinute);
                const overdueClass = isOverdue && !task.completed ? 'ring-2 ring-red-300' : '';

                // Format due time
                const dueTimeFormatted = formatTime(task.dueTime);
                const timeStatus = isOverdue && !task.completed ?
                    `<span class="text-red-600 font-medium">Overdue</span>` :
                    `Due: ${dueTimeFormatted}`;

                return `
                    <div class="task-item flex items-center gap-3 p-3 ${config.bg} rounded-lg ${config.border} border transition-all duration-200 hover:shadow-md ${overdueClass}"
                         style="${task.completed ? 'opacity: 0.6; text-decoration: line-through;' : ''}">
                        <input type="checkbox"
                               class="w-4 h-4 text-${task.priority === 'high' ? 'red' : task.priority === 'medium' ? 'yellow' : 'green'}-500 border-gray-300 rounded focus:ring-${task.priority === 'high' ? 'red' : task.priority === 'medium' ? 'yellow' : 'green'}-500"
                               onchange="completeTask(this, '${task.id}')"
                               ${task.completed ? 'checked' : ''}>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">${task.title}</p>
                            <p class="text-xs text-gray-600">${timeStatus}</p>
                            ${task.description ? `<p class="text-xs text-gray-500 mt-1">${task.description}</p>` : ''}
                        </div>
                        <div class="flex flex-col items-end gap-1">
                            <span class="px-2 py-1 ${config.badge} ${config.text} text-xs rounded-full capitalize">${task.priority}</span>
                            ${isOverdue && !task.completed ? '<i data-lucide="clock" class="w-3 h-3 text-red-500"></i>' : ''}
                        </div>
                    </div>
                `;
            }).join('');

            // Reinitialize Lucide icons
            lucide.createIcons();
        }

        function formatTime(timeString) {
            const [hour, minute] = timeString.split(':').map(Number);
            const period = hour >= 12 ? 'PM' : 'AM';
            const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
            return `${displayHour}:${minute.toString().padStart(2, '0')} ${period}`;
        }

        function refreshTasks() {
            const today = new Date();
            const dayKey = today.toDateString();
            const storedTasks = localStorage.getItem(`tasks_${dayKey}`);

            if (storedTasks) {
                const tasks = JSON.parse(storedTasks);
                renderTasks(tasks);
            }
        }

        // Clean up old task data (keep only last 7 days)
        function cleanupOldTasks() {
            const keys = Object.keys(localStorage);
            const taskKeys = keys.filter(key => key.startsWith('tasks_'));
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

            taskKeys.forEach(key => {
                const dateStr = key.replace('tasks_', '');
                const taskDate = new Date(dateStr);
                if (taskDate < sevenDaysAgo) {
                    localStorage.removeItem(key);
                }
            });
        }

        // --- Searchable Dropdown Component ---
        function createSearchableDropdown(containerId, options, placeholder = 'Search...', onSelect = null, compact = false) {
            const container = document.getElementById(containerId);
            if (!container) return;

            const originalSelect = container.querySelector('select');
            if (!originalSelect) return;

            // Check if already converted
            if (container.querySelector('.searchable-dropdown')) {
                return null;
            }

            // Hide original select completely
            originalSelect.style.display = 'none';
            originalSelect.style.visibility = 'hidden';
            originalSelect.style.position = 'absolute';
            originalSelect.style.left = '-9999px';

            // Determine if this should be a compact dropdown
            const isCompact = compact || originalSelect.classList.contains('text-sm') ||
                             originalSelect.closest('.pagination, .results-control, .compact');

            const compactClass = isCompact ? 'compact-dropdown' : '';
            const inputClass = isCompact ?
                'dropdown-search border border-gray-300 rounded px-2 py-1 text-sm pr-8' :
                'dropdown-search form-input w-full p-3 border-2 border-gray-200 rounded-xl focus-ring transition-all pr-10';

            // Create searchable dropdown structure
            const dropdownHtml = `
                <div class="searchable-dropdown relative ${compactClass}">
                    <div class="dropdown-input-container relative">
                        <input type="text"
                               class="${inputClass}"
                               placeholder="${placeholder}"
                               autocomplete="off">
                        <button type="button" class="dropdown-toggle absolute inset-y-0 right-0 flex items-center ${isCompact ? 'px-2' : 'px-3'} text-gray-400 hover:text-gray-600">
                            <i data-lucide="chevron-down" class="${isCompact ? 'w-4 h-4' : 'w-5 h-5'}"></i>
                        </button>
                    </div>
                    <div class="dropdown-menu absolute z-50 w-full mt-1 bg-white border border-gray-200 ${isCompact ? 'rounded' : 'rounded-xl'} shadow-lg max-h-60 overflow-y-auto hidden">
                        <div class="dropdown-options"></div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', dropdownHtml);

            const dropdown = container.querySelector('.searchable-dropdown');
            const searchInput = dropdown.querySelector('.dropdown-search');
            const toggleBtn = dropdown.querySelector('.dropdown-toggle');
            const menu = dropdown.querySelector('.dropdown-menu');
            const optionsContainer = dropdown.querySelector('.dropdown-options');

            let filteredOptions = [...options];
            let selectedValue = '';

            function renderOptions(optionsToRender = filteredOptions) {
                optionsContainer.innerHTML = optionsToRender.map(option => {
                    const value = typeof option === 'string' ? option : option.value;
                    const label = typeof option === 'string' ? option : option.label;
                    return `
                        <div class="dropdown-option px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm border-b border-gray-100 last:border-b-0"
                             data-value="${value}">
                            ${label}
                        </div>
                    `;
                }).join('');

                if (optionsToRender.length === 0) {
                    optionsContainer.innerHTML = '<div class="px-4 py-2 text-gray-500 text-sm">No options found</div>';
                }
            }

            function showDropdown() {
                menu.classList.remove('hidden');
                renderOptions();
                lucide.createIcons();
            }

            function hideDropdown() {
                menu.classList.add('hidden');
            }

            function selectOption(value, label) {
                selectedValue = value;
                searchInput.value = label;
                originalSelect.value = value;

                // Trigger change event on original select
                originalSelect.dispatchEvent(new Event('change', { bubbles: true }));

                if (onSelect) {
                    onSelect(value, label);
                }

                hideDropdown();
            }

            // Event listeners
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                filteredOptions = options.filter(option => {
                    const label = typeof option === 'string' ? option : option.label;
                    return label.toLowerCase().includes(searchTerm);
                });
                renderOptions();
            });

            searchInput.addEventListener('focus', showDropdown);
            toggleBtn.addEventListener('click', () => {
                menu.classList.contains('hidden') ? showDropdown() : hideDropdown();
            });

            // Handle option selection
            optionsContainer.addEventListener('click', (e) => {
                const option = e.target.closest('.dropdown-option');
                if (option && option.dataset.value) {
                    const value = option.dataset.value;
                    const label = option.textContent.trim();
                    selectOption(value, label);
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!dropdown.contains(e.target)) {
                    hideDropdown();
                }
            });

            // Initialize with first option if available
            renderOptions();
            lucide.createIcons();

            return {
                setValue: (value) => {
                    const option = options.find(opt =>
                        (typeof opt === 'string' ? opt : opt.value) === value
                    );
                    if (option) {
                        const label = typeof option === 'string' ? option : option.label;
                        selectOption(value, label);
                    }
                },
                getValue: () => selectedValue,
                updateOptions: (newOptions) => {
                    options = newOptions;
                    filteredOptions = [...options];
                    renderOptions();
                }
            };
        }

        // --- Universal Dropdown Converter ---
        function convertToSearchableDropdown(selectElement, options = null, placeholder = 'Search...', onSelect = null, compact = false) {
            if (!selectElement) return null;

            // Create container if it doesn't exist
            let container = selectElement.parentElement;
            if (!container.id) {
                const containerId = selectElement.id + '-container';
                const wrapper = document.createElement('div');
                wrapper.id = containerId;
                wrapper.className = container.className || '';
                selectElement.parentElement.insertBefore(wrapper, selectElement);
                wrapper.appendChild(selectElement);
                container = wrapper;
            }

            // Extract options from existing select if not provided
            if (!options) {
                options = Array.from(selectElement.options).map(option => ({
                    value: option.value,
                    label: option.textContent.trim()
                })).filter(opt => opt.value !== ''); // Remove empty options
            }

            // Auto-detect compact mode
            const isCompact = compact || selectElement.classList.contains('text-sm') ||
                             selectElement.closest('.pagination, .results-control, .compact') ||
                             selectElement.offsetHeight < 40;

            return createSearchableDropdown(container.id, options, placeholder, onSelect, isCompact);
        }

        function makeAllDropdownsSearchable() {
            // Find all select elements that aren't already converted
            const selectElements = document.querySelectorAll('select:not([data-searchable])');

            selectElements.forEach(select => {
                // Skip if already has a searchable dropdown sibling
                if (select.parentElement.querySelector('.searchable-dropdown')) {
                    return;
                }

                // Mark as processed
                select.setAttribute('data-searchable', 'true');

                // Convert based on select ID
                switch (select.id) {
                    case 'results-per-page':
                        convertToSearchableDropdown(
                            select,
                            dynamicData.resultsPerPageOptions,
                            'Search entries...',
                            (value) => {
                                // Handle results per page change
                                logActivity('results_per_page_changed', { value });
                                showToast(`Showing ${value} entries per page`, 'info', 2000);
                            },
                            true // Use compact mode
                        );
                        break;

                    default:
                        // Convert any other dropdown with default settings
                        convertToSearchableDropdown(select, null, 'Search options...');
                        break;
                }
            });
        }

        // --- Auto-detect and Convert New Dropdowns ---
        function scanForNewDropdowns() {
            // Use MutationObserver to detect new select elements
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if the added node is a select element
                            if (node.tagName === 'SELECT' && !node.hasAttribute('data-searchable')) {
                                convertToSearchableDropdown(node);
                            }

                            // Check for select elements within the added node
                            const selectElements = node.querySelectorAll ? node.querySelectorAll('select:not([data-searchable])') : [];
                            selectElements.forEach(select => {
                                convertToSearchableDropdown(select);
                            });

                            // Re-add enter key support for newly created dropdown inputs
                            setTimeout(() => {
                                addSerialSearchEnterSupport();
                            }, 100);
                        }
                    });
                });
            });

            // Start observing
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            return observer;
        }

        // --- Enhanced Dropdown Features ---
        function addDropdownEnhancements() {
            // Add keyboard shortcuts for dropdowns
            document.addEventListener('keydown', (e) => {
                // Alt + D to focus first dropdown
                if (e.altKey && e.key === 'd') {
                    e.preventDefault();
                    const firstDropdown = document.querySelector('.searchable-dropdown .dropdown-search');
                    if (firstDropdown) {
                        firstDropdown.focus();
                        showToast('Focused on first dropdown', 'info', 1500);
                    }
                }

                // Escape to close all dropdowns
                if (e.key === 'Escape') {
                    document.querySelectorAll('.dropdown-menu:not(.hidden)').forEach(menu => {
                        menu.classList.add('hidden');
                    });
                }
            });

            // Add visual feedback for dropdown interactions
            document.addEventListener('click', (e) => {
                if (e.target.closest('.searchable-dropdown')) {
                    const dropdown = e.target.closest('.searchable-dropdown');
                    dropdown.classList.add('active');
                    setTimeout(() => dropdown.classList.remove('active'), 200);
                }
            });
        }

        // --- Dynamic Serial Number Generation ---
        function generateSerialNumbers(year, model) {
            if (!year || !model) return [];

            const serialNumbers = [];
            const baseSerial = `${year}${model.toUpperCase().replace(/[^A-Z0-9]/g, '')}`;

            // Generate 10-15 serial numbers for the selected year/model
            const count = 10 + Math.floor(Math.random() * 6);

            for (let i = 1; i <= count; i++) {
                const serialSuffix = String(i).padStart(4, '0');
                serialNumbers.push({
                    value: `${baseSerial}${serialSuffix}`,
                    label: `${baseSerial}${serialSuffix}`
                });
            }

            return serialNumbers;
        }

        // --- Catalog Number Autocomplete ---
        function initializeCatalogAutocomplete() {
            const input = document.getElementById('catalogNumber');
            const suggestionsContainer = document.getElementById('catalog-suggestions');

            if (!input || !suggestionsContainer) return;

            let currentSuggestions = [];

            function showSuggestions(suggestions) {
                if (suggestions.length === 0) {
                    suggestionsContainer.classList.add('hidden');
                    return;
                }

                suggestionsContainer.innerHTML = suggestions.map(catalog => `
                    <div class="catalog-suggestion px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm border-b border-gray-100 last:border-b-0"
                         data-value="${catalog}">
                        ${catalog}
                    </div>
                `).join('');

                suggestionsContainer.classList.remove('hidden');
            }

            function hideSuggestions() {
                suggestionsContainer.classList.add('hidden');
            }

            input.addEventListener('input', (e) => {
                const value = e.target.value.trim();

                if (value.length < 2) {
                    hideSuggestions();
                    return;
                }

                // Filter catalog numbers based on input
                const filtered = dynamicData.catalogNumbers.filter(catalog =>
                    catalog.toLowerCase().includes(value.toLowerCase())
                ).slice(0, 10); // Show max 10 suggestions

                currentSuggestions = filtered;
                showSuggestions(filtered);
            });

            input.addEventListener('focus', () => {
                if (currentSuggestions.length > 0) {
                    showSuggestions(currentSuggestions);
                }
            });

            // Handle suggestion selection
            suggestionsContainer.addEventListener('click', (e) => {
                const suggestion = e.target.closest('.catalog-suggestion');
                if (suggestion) {
                    input.value = suggestion.dataset.value;
                    hideSuggestions();
                    input.focus();
                }
            });

            // Hide suggestions when clicking outside
            document.addEventListener('click', (e) => {
                if (!input.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                    hideSuggestions();
                }
            });

            // Handle keyboard navigation
            input.addEventListener('keydown', (e) => {
                const suggestions = suggestionsContainer.querySelectorAll('.catalog-suggestion');
                const activeSuggestion = suggestionsContainer.querySelector('.catalog-suggestion.bg-gray-200');

                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    if (!activeSuggestion) {
                        suggestions[0]?.classList.add('bg-gray-200');
                    } else {
                        activeSuggestion.classList.remove('bg-gray-200');
                        const next = activeSuggestion.nextElementSibling || suggestions[0];
                        next.classList.add('bg-gray-200');
                    }
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    if (!activeSuggestion) {
                        suggestions[suggestions.length - 1]?.classList.add('bg-gray-200');
                    } else {
                        activeSuggestion.classList.remove('bg-gray-200');
                        const prev = activeSuggestion.previousElementSibling || suggestions[suggestions.length - 1];
                        prev.classList.add('bg-gray-200');
                    }
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    if (activeSuggestion) {
                        input.value = activeSuggestion.dataset.value;
                        hideSuggestions();
                    }
                } else if (e.key === 'Escape') {
                    hideSuggestions();
                }
            });
        }

        // --- Navigation Functions ---
        function navigateToSection(section) {
            logActivity('navigate_to_section', { section: section });
            showToast(`Navigating to ${section}...`, 'info');
        }



        // --- Activity Log Modal ---
        function showActivityLog() {
            const modal = document.getElementById('activity-log-modal');
            const content = document.getElementById('activity-log-content');

            // Populate activity log
            content.innerHTML = activityLog.map(activity => `
                <div class="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div class="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                        <i data-lucide="activity" class="w-4 h-4 text-blue-600"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-gray-900">${activity.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
                        <p class="text-xs text-gray-600">${new Date(activity.timestamp).toLocaleString()}</p>
                        ${activity.details && Object.keys(activity.details).length > 0 ?
                            `<p class="text-xs text-gray-500 mt-1">${JSON.stringify(activity.details)}</p>` : ''}
                    </div>
                </div>
            `).join('');

            modal.classList.remove('hidden');
            logActivity('view_activity_log');
        }

        function closeActivityLog() {
            document.getElementById('activity-log-modal').classList.add('hidden');
        }

        // Log initial page load
        logActivity('page_load', { page: 'home' });


        // --- Enhanced Tab Switching Logic ---
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        function switchTab(targetTabId) {
            tabButtons.forEach(btn => {
                const isActive = btn.dataset.tab === targetTabId;
                if (isActive) {
                    btn.style.color = '#f2b90c';
                    btn.style.borderBottomColor = '#f2b90c';
                    btn.classList.remove('text-gray-500', 'hover:text-gray-700');
                } else {
                    btn.style.color = '';
                    btn.style.borderBottomColor = '';
                    btn.classList.add('text-gray-500', 'hover:text-gray-700');
                }
                btn.setAttribute('aria-selected', isActive);
            });

            tabContents.forEach(content => {
                const isVisible = content.id === targetTabId;
                content.classList.toggle('hidden', !isVisible);
                if (isVisible) {
                    content.style.animation = 'fadeIn 0.3s ease-in-out';
                }
            });

            // Update breadcrumb
            updateBreadcrumb(targetTabId);

            showToast(`Switched to ${targetTabId.replace(/([A-Z])/g, ' $1').toLowerCase()}`, 'info', 2000);
        }

        function updateBreadcrumb(tabId) {
            const breadcrumbMap = {
                'numberEnquiry': 'Parts Number Enquiry',
                'priceEnquiry': 'Price Enquiry',
                'aiSearch': 'AI-Powered Search',
                'cart': 'Shopping Cart'
            };

            const currentPageElement = document.getElementById('current-page');
            if (currentPageElement && breadcrumbMap[tabId]) {
                currentPageElement.textContent = breadcrumbMap[tabId];
            }
        }

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                switchTab(button.dataset.tab);
            });

            // Keyboard navigation
            button.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    switchTab(button.dataset.tab);
                }
            });
        });

        // --- Enhanced AI Modal Logic ---
        const modal = document.getElementById('ai-modal');
        const modalBackdrop = document.getElementById('ai-modal-backdrop');
        const modalPanel = document.getElementById('ai-modal-panel');
        const closeModalButton = document.getElementById('close-ai-modal');
        const modalTitle = document.getElementById('ai-modal-title');
        const modalLoading = document.getElementById('ai-modal-loading');
        const modalContent = document.getElementById('ai-modal-content');

        function showModal() {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            setTimeout(() => {
                modalBackdrop.classList.remove('opacity-0');
                modalPanel.classList.remove('opacity-0', 'scale-95');
            }, 10);
        }

        function hideModal() {
            modalBackdrop.classList.add('opacity-0');
            modalPanel.classList.add('opacity-0', 'scale-95');
            document.body.style.overflow = '';

            setTimeout(() => modal.classList.add('hidden'), 300);
        }

        function setModalLoading(isLoading) {
            modalLoading.classList.toggle('hidden', !isLoading);
            modalContent.classList.toggle('hidden', isLoading);
        }

        // Enhanced modal event listeners
        closeModalButton.addEventListener('click', hideModal);
        modalBackdrop.addEventListener('click', hideModal);

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                hideModal();
            }
        });

        // --- Form Enhancement Functions ---
        function validateForm(formElement) {
            const inputs = formElement.querySelectorAll('input[required], textarea[required], select[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('border-red-500');
                    isValid = false;
                } else {
                    input.classList.remove('border-red-500');
                }
            });

            return isValid;
        }

        function addFormLoadingState(button) {
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = `
                <div class="flex items-center justify-center gap-2">
                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Processing...</span>
                </div>
            `;

            return () => {
                button.disabled = false;
                button.innerHTML = originalText;
                lucide.createIcons();
            };
        }

        // --- Enhanced API Call Function ---
        async function callGeminiAPI(prompt, isJson = false) {
            const apiKey = ""; // Leave blank for demo

            // Simulate API call for demo purposes
            if (!apiKey) {
                await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate delay

                if (isJson) {
                    return JSON.stringify([
                        { partNumber: "LP-2024-001", reason: "High-performance drive belt suitable for heavy-duty applications" },
                        { partNumber: "LP-2024-002", reason: "Reinforced belt with enhanced durability for continuous operation" },
                        { partNumber: "LP-2024-003", reason: "Standard belt for general purpose weaving machines" }
                    ]);
                } else {
                    return `
# Part Analysis Report

## Overview
This appears to be an industrial machine part commonly used in textile manufacturing equipment.

## Key Features
- **Material**: High-grade steel alloy
- **Application**: Primary drive systems
- **Compatibility**: Model X series weavers
- **Durability**: Rated for 10,000+ operating hours

## Maintenance Tips
- Regular lubrication every 500 hours
- Visual inspection for wear patterns
- Replace when tolerance exceeds 0.1mm

## Installation Notes
- Requires specialized tools
- Follow torque specifications: 45-50 Nm
- Ensure proper alignment before operation
                    `;
                }
            }

            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            let payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };

            if (isJson) {
                payload.generationConfig = {
                    responseMimeType: "application/json",
                    responseSchema: {
                        type: "ARRAY",
                        items: {
                            type: "OBJECT",
                            properties: {
                                partNumber: { "type": "STRING" },
                                reason: { "type": "STRING" }
                            },
                            required: ["partNumber", "reason"]
                        }
                    }
                };
            }

            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    throw new Error(`API call failed with status: ${response.status}`);
                }

                const result = await response.json();

                if (result.candidates && result.candidates[0].content && result.candidates[0].content.parts[0]) {
                    return result.candidates[0].content.parts[0].text;
                } else {
                    console.error("Unexpected API response structure:", result);
                    throw new Error("Invalid response structure from AI service");
                }
            } catch (error) {
                console.error("Error calling Gemini API:", error);
                showToast(`AI service error: ${error.message}`, 'error');
                throw error;
            }
        }

        // --- Enhanced Form Handlers ---

        // Price Enquiry Form
        const priceEnquiryForm = document.getElementById('priceEnquiryForm');
        priceEnquiryForm?.addEventListener('submit', async (e) => {
            e.preventDefault();
            const partNumber = document.getElementById('partNumber').value.trim();

            if (!partNumber) {
                showToast('Please enter a part number', 'warning');
                return;
            }

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const resetLoading = addFormLoadingState(submitBtn);

            try {
                // Simulate price lookup
                await new Promise(resolve => setTimeout(resolve, 1500));

                const mockPrice = (Math.random() * 500 + 50).toFixed(2);
                const availability = Math.random() > 0.3 ? 'In Stock' : 'Limited Stock';

                showToast(`Part ${partNumber}: $${mockPrice} - ${availability}`, 'success', 5000);
            } catch (error) {
                showToast('Failed to fetch price information', 'error');
            } finally {
                resetLoading();
            }
        });

        // AI Analysis Feature
        const getAiAnalysisBtn = document.getElementById('getAiAnalysis');
        getAiAnalysisBtn?.addEventListener('click', async () => {
            const partNumber = document.getElementById('partNumber').value.trim();

            if (!partNumber) {
                showToast('Please enter a Part Number first', 'warning');
                return;
            }

            const resetLoading = addFormLoadingState(getAiAnalysisBtn);
            modalTitle.innerText = `🔍 AI Analysis for Part #${partNumber}`;
            showModal();
            setModalLoading(true);

            try {
                const prompt = `Provide a detailed analysis for an industrial machine part with the number '${partNumber}'. Include its likely function, common applications, typical materials, and potential maintenance or installation tips. Format the response in clear, well-structured markdown.`;
                const result = await callGeminiAPI(prompt);

                // Enhanced markdown to HTML conversion
                let htmlResult = result
                    .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold text-gray-900 mb-3 mt-6">$1</h3>')
                    .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold text-gray-900 mb-4 mt-8">$1</h2>')
                    .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-gray-900 mb-6">$1</h1>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em class="italic text-gray-700">$1</em>')
                    .replace(/`(.*?)`/g, '<code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">$1</code>')
                    .replace(/^- (.*$)/gim, '<li class="ml-4 mb-1">$1</li>')
                    .replace(/\n\n/g, '</p><p class="mb-4">')
                    .replace(/\n/g, '<br>');

                modalContent.innerHTML = `<div class="prose prose-lg max-w-none"><p class="mb-4">${htmlResult}</p></div>`;
                showToast('AI analysis completed', 'success');
            } catch (error) {
                modalContent.innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-red-500 mb-4">
                            <i data-lucide="alert-circle" class="w-12 h-12 mx-auto mb-2"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Analysis Failed</h3>
                        <p class="text-gray-600">Unable to analyze part ${partNumber}. Please try again later.</p>
                    </div>
                `;
                lucide.createIcons();
            } finally {
                setModalLoading(false);
                resetLoading();
            }
        });

        // AI-Powered Part Search
        const aiSearchForm = document.getElementById('aiSearchForm');
        aiSearchForm?.addEventListener('submit', async (e) => {
            e.preventDefault();
            const description = document.getElementById('partDescription').value.trim();

            if (!description) {
                showToast('Please describe the part you are looking for', 'warning');
                return;
            }

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const resetLoading = addFormLoadingState(submitBtn);

            modalTitle.innerText = '✨ AI Part Number Suggestions';
            showModal();
            setModalLoading(true);

            try {
                const prompt = `Based on the description "${description}", suggest possible industrial machine part numbers. Provide a list of potential matches with a brief reason for each suggestion.`;
                const jsonString = await callGeminiAPI(prompt, true);
                const parts = JSON.parse(jsonString);

                let contentHtml = `
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">🎯 Suggested Parts</h3>
                        <p class="text-gray-600">Based on your description: "${description}"</p>
                    </div>
                    <div class="space-y-4">
                `;

                parts.forEach((part, index) => {
                    contentHtml += `
                        <div class="p-4 bg-blue-50 rounded-xl border border-blue-200 hover:shadow-md transition-shadow">
                            <div class="flex items-start gap-3">
                                <div class="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                                    ${index + 1}
                                </div>
                                <div class="flex-1">
                                    <p class="font-semibold text-gray-900 mb-1">
                                        Part Number: <code class="bg-white text-blue-600 px-3 py-1 rounded-lg text-sm font-mono">${part.partNumber}</code>
                                    </p>
                                    <p class="text-sm text-gray-700"><strong>Match Reason:</strong> ${part.reason}</p>
                                    <div class="mt-2 flex gap-2">
                                        <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors">
                                            Check Price
                                        </button>
                                        <button class="text-xs bg-gray-500 text-white px-3 py-1 rounded-full hover:bg-gray-600 transition-colors">
                                            View Details
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                contentHtml += '</div>';
                modalContent.innerHTML = contentHtml;
                showToast('AI search completed successfully', 'success');
            } catch (error) {
                modalContent.innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-yellow-500 mb-4">
                            <i data-lucide="search-x" class="w-12 h-12 mx-auto mb-2"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">No Results Found</h3>
                        <p class="text-gray-600 mb-4">The AI couldn't find matching parts for your description.</p>
                        <p class="text-sm text-gray-500">Try being more specific about the part's function, size, or application.</p>
                    </div>
                `;
                lucide.createIcons();
            } finally {
                setModalLoading(false);
                resetLoading();
            }
        });

        // --- Search Results Grid Functions ---
        const searchResultsContainer = document.getElementById('search-results-container');
        const searchResultsTbody = document.getElementById('search-results-tbody');
        const resultsCount = document.getElementById('results-count');
        const proceedBtn = document.getElementById('proceed-btn');
        const addToCartBtn = document.getElementById('add-to-cart-btn');
        const clearSelectionBtn = document.getElementById('clear-selection-btn');

        // Sample search results data
        const sampleSearchResults = [
            {
                partNumber: '1117300009',
                description: 'Love Joy Coupling Type 1095',
                hsn: '8477',
                moq: 1,
                lotSize: 1,
                unitPrice: 'INR 255.00',
                spMoq: '0.00',
                cgst: '18',
                uom: 'EA',
                salesOrg: '1000',
                distChannel: 'AA',
                plant: '',
                commodityCode: '' 
            },
            {
                partNumber: '1117300010',
                description: 'Oil Seal Bolt 27',
                hsn: '7318',
                moq: 1,
                lotSize: 1,
                unitPrice: 'INR 50.00',
                spMoq: '0.00',
                cgst: '18',
                uom: 'EA',
                salesOrg: '1000',
                distChannel: 'AA',
                plant: '',
                commodityCode: ''
            },
            {
                partNumber: '1117300020',
                description: 'Barrel Inlet Jet, 350 x 7 Dia (D 75, 2016',
                hsn: '8488',
                moq: 1,
                lotSize: 1,
                unitPrice: 'INR 14,850.00',
                spMoq: '14850.00',
                cgst: '18',
                uom: 'EA',
                salesOrg: '1000',
                distChannel: 'AA',
                plant: '',
                commodityCode: ''
            },
            {
                partNumber: '1117300030',
                description: 'Cam Shaft Compl 240V 1 Inch (D 75, 2016',
                hsn: '8469',
                moq: 1,
                lotSize: 1,
                unitPrice: 'INR 1,380.00',
                spMoq: '0.00',
                cgst: '18',
                uom: 'EA',
                salesOrg: '1000',
                distChannel: 'AA',
                plant: '',
                commodityCode: ''
            }
        ];

        function displaySearchResults(results) {
            searchResultsTbody.innerHTML = '';
            resultsCount.textContent = results.length;

            results.forEach((result, index) => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors border-b border-gray-200';
                row.innerHTML = `
                    <td class="p-1 text-center border-r border-gray-300">
                        <input type="checkbox" class="result-checkbox w-3 h-3 text-yellow-500 border-gray-300 rounded focus:ring-yellow-500"
                               data-index="${index}" onchange="updateActionButtons()">
                    </td>
                    <td class="p-1 font-medium text-blue-600 border-r border-gray-300 truncate text-xs" title="${result.partNumber}">${result.partNumber}</td>
                    <td class="p-1 border-r border-gray-300 truncate text-xs" title="${result.description}">${result.description}</td>
                    <td class="p-1 text-center border-r border-gray-300 text-xs">${result.hsn}</td>
                    <td class="p-1 text-center border-r border-gray-300 text-xs">${result.moq}</td>
                    <td class="p-1 text-center border-r border-gray-300 text-xs">${result.lotSize}</td>
                    <td class="p-1 text-right font-semibold border-r border-gray-300 text-xs whitespace-nowrap">${result.unitPrice}</td>
                    <td class="p-1 text-right border-r border-gray-300 text-xs">${result.spMoq}</td>
                    <td class="p-1 text-center border-r border-gray-300">${result.cgst}</td>
                    <td class="p-1 text-center border-r border-gray-300">${result.uom}</td>
                    <td class="p-1 text-center border-r border-gray-300">${result.salesOrg}</td>
                    <td class="p-1 text-center border-r border-gray-300">${result.distChannel}</td>
                    <td class="p-1 text-center border-r border-gray-300">${result.plant}</td>
                    <td class="p-1 text-center">${result.commodityCode}</td>
                `;
                searchResultsTbody.appendChild(row);
            });

            searchResultsContainer.classList.remove('hidden');
            searchResultsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
            updateActionButtons();
        }

        function updateActionButtons() {
            const checkedBoxes = document.querySelectorAll('.result-checkbox:checked');
            const hasSelection = checkedBoxes.length > 0;

            proceedBtn.disabled = !hasSelection;
            addToCartBtn.disabled = !hasSelection;

            if (hasSelection) {
                proceedBtn.classList.remove('opacity-50');
                addToCartBtn.classList.remove('opacity-50');
            } else {
                proceedBtn.classList.add('opacity-50');
                addToCartBtn.classList.add('opacity-50');
            }
        }

        function performSearch(searchType, searchValue) {
            // Simulate search delay
            setTimeout(() => {
                // Filter results based on search value (simple simulation)
                let filteredResults = sampleSearchResults;
                if (searchValue && searchValue.trim()) {
                    filteredResults = sampleSearchResults.filter(result =>
                        result.partNumber.toLowerCase().includes(searchValue.toLowerCase()) ||
                        result.description.toLowerCase().includes(searchValue.toLowerCase())
                    );
                }

                displaySearchResults(filteredResults);
                showToast(`Found ${filteredResults.length} matching parts`, 'success');
            }, 500);
        }

        // Action button handlers
        proceedBtn?.addEventListener('click', () => {
            const selectedItems = document.querySelectorAll('.result-checkbox:checked');
            showToast(`Proceeding with ${selectedItems.length} selected items`, 'info');
        });

        addToCartBtn?.addEventListener('click', () => {
            const selectedItems = document.querySelectorAll('.result-checkbox:checked');

            if (selectedItems.length === 0) {
                showToast('Please select at least one item to add to cart', 'warning');
                return;
            }

            // Extract selected item data
            const itemsData = Array.from(selectedItems).map(checkbox => {
                const row = checkbox.closest('tr');
                return {
                    partNumber: row.cells[1].textContent.trim(),
                    description: row.cells[2].textContent.trim(),
                    moq: parseInt(row.cells[4].textContent.trim()) || 1,
                    lotSize: parseInt(row.cells[5].textContent.trim()) || 1,
                    unitPrice: parseFloat(row.cells[6].textContent.replace('₹', '').trim()) || 0,
                    gst: 18,
                    quantity: 1
                };
            });

            // Open cart modal with selected items
            openCartModal(itemsData);
            showToast(`Added ${selectedItems.length} items to cart`, 'success');
        });

        clearSelectionBtn?.addEventListener('click', () => {
            document.querySelectorAll('.result-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            updateActionButtons();
            showToast('Selection cleared', 'info');
        });

        // Search button event listeners
        document.getElementById('catalog-search-btn')?.addEventListener('click', () => {
            const catalogNumber = document.getElementById('catalogNumber').value.trim();
            if (!catalogNumber) {
                showToast('Please enter a catalog number', 'warning');
                return;
            }
            showToast('Searching for parts...', 'info', 2000);
            performSearch('catalog', catalogNumber);
        });

        document.getElementById('serial-search-btn')?.addEventListener('click', () => {
            const mfgYear = document.getElementById('mfgYear').value;
            const modelDesc = document.getElementById('modelDesc').value;
            const serialNumber = document.getElementById('serialNumber').value;

            if (!mfgYear || !modelDesc) {
                showToast('Please select manufacturing year and model description', 'warning');
                return;
            }

            showToast('Searching for parts...', 'info', 2000);
            performSearch('serial', `${mfgYear}-${modelDesc}-${serialNumber}`);
        });

        // Add Enter key support for catalog search
        document.getElementById('catalogNumber')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('catalog-search-btn').click();
            }
        });

        // Add Enter key support for part number in price enquiry
        document.getElementById('partNumber')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const priceForm = document.getElementById('priceEnquiryForm');
                if (priceForm) {
                    // Trigger form submission
                    const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                    priceForm.dispatchEvent(submitEvent);
                }
            }
        });

        // Add Enter key support for part description in AI search
        document.getElementById('partDescription')?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                const aiForm = document.getElementById('aiSearchForm');
                if (aiForm) {
                    // Trigger form submission with Ctrl+Enter or Cmd+Enter
                    const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                    aiForm.dispatchEvent(submitEvent);
                }
            }
        });

        // Add Enter key support for serial number search
        function addSerialSearchEnterSupport() {
            const serialNumberInput = document.querySelector('#serialNumber-container .dropdown-search');
            if (serialNumberInput) {
                serialNumberInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        const mfgYear = document.getElementById('mfgYear').value;
                        const modelDesc = document.getElementById('modelDesc').value;

                        if (mfgYear && modelDesc) {
                            document.getElementById('serial-search-btn')?.click();
                        } else {
                            showToast('Please select manufacturing year and model description first', 'warning');
                        }
                    }
                });
            }
        }

        // Add Enter key support for all searchable dropdown inputs
        function addDropdownEnterSupport() {
            document.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && e.target.classList.contains('dropdown-search')) {
                    const container = e.target.closest('.searchable-dropdown');
                    if (container) {
                        const dropdown = container.querySelector('.dropdown-menu');
                        const firstOption = dropdown?.querySelector('.dropdown-option:not(.hidden)');
                        if (firstOption && !dropdown.classList.contains('hidden')) {
                            firstOption.click();
                        }
                    }
                }
            });
        }

        // --- Cart Modal Functions ---
        function openCartModal(selectedItems = []) {
            const modal = document.getElementById('cart-modal');

            // Generate cart data
            const cartData = generateCartData(selectedItems);

            // Populate cart information
            document.getElementById('cart-number').textContent = cartData.cartNumber;
            document.getElementById('customer-code').textContent = cartData.customerCode;
            document.getElementById('submitted-date').textContent = cartData.submittedDate;

            // Populate cart items
            populateCartItems(cartData.items);

            // Update totals
            updateCartTotals(cartData.items);

            // Show modal
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // Reinitialize icons
            lucide.createIcons();

            logActivity('cart_modal_opened', { itemCount: selectedItems.length });
        }

        function closeCartModal() {
            const modal = document.getElementById('cart-modal');
            modal.classList.add('hidden');
            document.body.style.overflow = '';
            logActivity('cart_modal_closed');
        }

        function generateCartData(selectedItems) {
            const now = new Date();
            const cartNumber = `${Math.floor(Math.random() * 90000) + 10000}/06/25/22`;
            const customerCode = Math.floor(Math.random() * 90000) + 10000;
            const submittedDate = now.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });

            // Generate realistic cart items based on selected items or sample data
            const items = selectedItems.length > 0 ?
                selectedItems.map((item, index) => ({
                    slNo: index + 1,
                    partNumber: item.partNumber || `LP-2024-${String(index + 1).padStart(3, '0')}`,
                    description: item.description || `Industrial Component ${index + 1}`,
                    moq: item.moq || Math.floor(Math.random() * 500) + 1,
                    lotSize: item.lotSize || Math.floor(Math.random() * 500) + 1,
                    unitPrice: item.unitPrice || (Math.random() * 1000 + 50).toFixed(2),
                    gst: item.gst || 18,
                    orderQuantity: item.quantity || Math.floor(Math.random() * 10) + 1,
                    totalAmount: 0,
                    gstAmount: 0,
                    lineTotal: 0
                })) :
                [
                    {
                        slNo: 1,
                        partNumber: '1100523112',
                        description: 'Industrial Blade 0.1 mm',
                        moq: 500,
                        lotSize: 500,
                        unitPrice: 1.30,
                        gst: 18,
                        orderQuantity: 1,
                        totalAmount: 0,
                        gstAmount: 0,
                        lineTotal: 0
                    },
                    {
                        slNo: 2,
                        partNumber: '1111290950',
                        description: 'Love Joy Coupling Type L095',
                        moq: 1,
                        lotSize: 1,
                        unitPrice: 925.00,
                        gst: 18,
                        orderQuantity: 1,
                        totalAmount: 0,
                        gstAmount: 0,
                        lineTotal: 0
                    },
                    {
                        slNo: 3,
                        partNumber: '1112061910',
                        description: 'SS hex bolt FT',
                        moq: 1,
                        lotSize: 1,
                        unitPrice: 50.00,
                        gst: 18,
                        orderQuantity: 1,
                        totalAmount: 0,
                        gstAmount: 0,
                        lineTotal: 0
                    }
                ];

            // Calculate amounts for each item
            items.forEach(item => {
                item.totalAmount = (parseFloat(item.unitPrice) * item.orderQuantity).toFixed(2);
                item.gstAmount = (parseFloat(item.totalAmount) * item.gst / 100).toFixed(2);
                item.lineTotal = (parseFloat(item.totalAmount) + parseFloat(item.gstAmount)).toFixed(2);
            });

            return {
                cartNumber,
                customerCode,
                submittedDate,
                items
            };
        }

        function populateCartItems(items) {
            const tbody = document.getElementById('cart-items-tbody');

            tbody.innerHTML = items.map(item => `
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                    <td class="text-center p-3 border-r border-gray-300">${item.slNo}</td>
                    <td class="text-left p-3 border-r border-gray-300 font-medium">${item.partNumber}</td>
                    <td class="text-left p-3 border-r border-gray-300">${item.description}</td>
                    <td class="text-center p-3 border-r border-gray-300">${item.moq}</td>
                    <td class="text-center p-3 border-r border-gray-300">${item.lotSize}</td>
                    <td class="text-right p-3 border-r border-gray-300">₹${parseFloat(item.unitPrice).toFixed(2)}</td>
                    <td class="text-center p-3 border-r border-gray-300">${item.gst}%</td>
                    <td class="text-center p-3 border-r border-gray-300">
                        <input type="number" value="${item.orderQuantity}" min="1"
                               class="w-20 px-2 py-1 border border-gray-300 rounded text-center"
                               onchange="updateItemQuantity(${item.slNo}, this.value)">
                    </td>
                    <td class="text-right p-3 border-r border-gray-300">₹${item.totalAmount}</td>
                    <td class="text-right p-3 border-r border-gray-300">₹${item.gstAmount}</td>
                    <td class="text-right p-3 font-semibold">₹${item.lineTotal}</td>
                </tr>
            `).join('');
        }

        function updateCartTotals(items) {
            const subtotal = items.reduce((sum, item) => sum + parseFloat(item.totalAmount), 0);
            const gstTotal = items.reduce((sum, item) => sum + parseFloat(item.gstAmount), 0);
            const total = subtotal + gstTotal;

            document.getElementById('cart-subtotal').textContent = `₹${subtotal.toFixed(2)}`;
            document.getElementById('cart-gst').textContent = `₹${gstTotal.toFixed(2)}`;
            document.getElementById('cart-total').textContent = `₹${total.toFixed(2)}`;
        }

        function updateItemQuantity(slNo, newQuantity) {
            // This would update the item quantity and recalculate totals
            showToast(`Updated quantity for item ${slNo} to ${newQuantity}`, 'info');
            logActivity('cart_item_quantity_updated', { slNo, newQuantity });
        }

        function proceedToCheckout() {
            closeCartModal();
            showToast('Proceeding to checkout...', 'info');
            logActivity('proceed_to_checkout');

            // Simulate navigation to checkout
            setTimeout(() => {
                showToast('Checkout page would open here', 'success');
            }, 1000);
        }

        function saveCart() {
            const cartRows = document.querySelectorAll('#cart-items-tbody tr');
            if (cartRows.length === 0) {
                showToast('No items in cart to save', 'warning');
                return;
            }

            const cartData = {
                id: `cart_${Date.now()}`,
                cartNumber: document.getElementById('cart-number').textContent,
                customerCode: document.getElementById('customer-code').textContent,
                timestamp: new Date().toISOString(),
                createdDate: document.getElementById('submitted-date').textContent,
                status: 'Pending',
                items: Array.from(cartRows).map((row, index) => ({
                    slNo: index + 1,
                    partNumber: row.cells[1].textContent.trim(),
                    description: row.cells[2].textContent.trim(),
                    moq: row.cells[3].textContent.trim(),
                    lotSize: row.cells[4].textContent.trim(),
                    unitPrice: row.cells[5].textContent.trim(),
                    gst: row.cells[6].textContent.trim(),
                    quantity: row.querySelector('input').value,
                    totalAmount: row.cells[8].textContent.trim(),
                    gstAmount: row.cells[9].textContent.trim(),
                    lineTotal: row.cells[10].textContent.trim()
                })),
                totals: {
                    subtotal: document.getElementById('cart-subtotal').textContent,
                    gst: document.getElementById('cart-gst').textContent,
                    total: document.getElementById('cart-total').textContent
                }
            };

            // Get existing saved carts
            const savedCarts = JSON.parse(localStorage.getItem('savedCarts') || '[]');
            savedCarts.unshift(cartData);

            // Keep only last 50 carts
            if (savedCarts.length > 50) {
                savedCarts.splice(50);
            }

            localStorage.setItem('savedCarts', JSON.stringify(savedCarts));

            // Close modal and navigate to cart page
            closeCartModal();
            window.location.href = 'Cart.html';

            showToast('Cart saved successfully!', 'success');
            logActivity('cart_saved', {
                cartId: cartData.id,
                itemCount: cartData.items.length,
                total: cartData.totals.total
            });
        }



        // --- Cleanup Function ---
        function cleanupDuplicateInputs() {
            // Remove any duplicate search inputs that might have been created
            document.querySelectorAll('.searchable-dropdown').forEach(dropdown => {
                const container = dropdown.parentElement;
                const inputs = container.querySelectorAll('input[type="text"]');

                // If there are multiple text inputs, keep only the searchable dropdown one
                if (inputs.length > 1) {
                    inputs.forEach(input => {
                        if (!input.classList.contains('dropdown-search')) {
                            input.style.display = 'none';
                            input.style.visibility = 'hidden';
                            input.style.position = 'absolute';
                            input.style.left = '-9999px';
                        }
                    });
                }
            });
        }

        // --- Initialize Dynamic Components ---
        function initializeDynamicComponents() {
            // Initialize main searchable dropdowns
            const mfgYearDropdown = createSearchableDropdown(
                'mfgYear-container',
                dynamicData.manufacturingYears,
                'Search manufacturing year...',
                (value) => {
                    // Update model descriptions when year changes
                    const modelDropdown = document.querySelector('#modelDesc-container .searchable-dropdown');
                    if (modelDropdown) {
                        updateModelDescriptions();
                    }
                }
            );

            const modelDescDropdown = createSearchableDropdown(
                'modelDesc-container',
                dynamicData.modelDescriptions,
                'Search model description...',
                (value) => {
                    // Update serial numbers when model changes
                    updateSerialNumbers();
                }
            );

            const serialNumberDropdown = createSearchableDropdown(
                'serialNumber-container',
                [],
                'Search serial number...'
            );

            // Convert ALL remaining dropdowns to searchable
            makeAllDropdownsSearchable();

            // Clean up any duplicate inputs
            setTimeout(cleanupDuplicateInputs, 100);

            // Initialize catalog autocomplete
            initializeCatalogAutocomplete();

            // Initialize recent activity
            renderRecentActivity();

            // Add dropdown enhancements
            addDropdownEnhancements();

            // Add enter key support for dropdowns and inputs
            addDropdownEnterSupport();
            addSerialSearchEnterSupport();

            // Start scanning for new dropdowns
            window.dropdownObserver = scanForNewDropdowns();

            // Store dropdown references for later use
            window.dropdownInstances = {
                mfgYear: mfgYearDropdown,
                modelDesc: modelDescDropdown,
                serialNumber: serialNumberDropdown
            };

            // Log successful initialization
            logActivity('dropdowns_initialized', {
                count: document.querySelectorAll('.searchable-dropdown').length
            });
        }

        function updateModelDescriptions() {
            const mfgYearValue = document.getElementById('mfgYear').value;
            if (!mfgYearValue) return;

            // Filter models based on year (newer models for newer years)
            const yearNum = parseInt(mfgYearValue);
            let availableModels = [...dynamicData.modelDescriptions];

            if (yearNum >= 2020) {
                // Newer years have all models
                availableModels = dynamicData.modelDescriptions;
            } else if (yearNum >= 2015) {
                // Mid-range years have most models except ultra series
                availableModels = dynamicData.modelDescriptions.filter(model =>
                    !model.value.includes('6500') && !model.value.includes('6000')
                );
            } else {
                // Older years have basic models only
                availableModels = dynamicData.modelDescriptions.filter(model =>
                    model.category === 'standard' || model.category === 'compact' || model.category === 'spinning'
                );
            }

            if (window.dropdownInstances?.modelDesc) {
                window.dropdownInstances.modelDesc.updateOptions(availableModels);
            }
        }

        function updateSerialNumbers() {
            const mfgYear = document.getElementById('mfgYear').value;
            const modelDesc = document.getElementById('modelDesc').value;

            if (mfgYear && modelDesc) {
                const serialNumbers = generateSerialNumbers(mfgYear, modelDesc);
                if (window.dropdownInstances?.serialNumber) {
                    window.dropdownInstances.serialNumber.updateOptions(serialNumbers);
                }
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            showToast('Welcome to Lohia Order Management System', 'success', 4000);

            // Initialize all dynamic components
            initializeDynamicComponents();

            // Initialize dynamic tasks system
            cleanupOldTasks();
            generateDynamicTasks();

            // Refresh tasks every 5 minutes to update overdue status
            setInterval(refreshTasks, 5 * 60 * 1000);

            // Refresh tasks every hour to check for new time-based tasks
            setInterval(() => {
                const today = new Date();
                const dayKey = today.toDateString();
                const storedTasks = localStorage.getItem(`tasks_${dayKey}`);
                if (storedTasks) {
                    refreshTasks();
                }
            }, 60 * 60 * 1000);

            // Refresh recent activity every 10 minutes
            setInterval(renderRecentActivity, 10 * 60 * 1000);

            // Add form validation on input
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    if (input.hasAttribute('required') && !input.value.trim()) {
                        input.classList.add('border-red-300');
                    } else {
                        input.classList.remove('border-red-300');
                    }
                });
            });

            // Initialize cart count
            const savedCarts = JSON.parse(localStorage.getItem('savedCarts') || '[]');
            document.getElementById('cart-count').textContent = savedCarts.length;
        });
    </script>
</body>
</html>
